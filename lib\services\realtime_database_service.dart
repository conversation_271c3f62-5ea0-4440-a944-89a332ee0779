import 'package:firebase_database/firebase_database.dart';
import '../models/user_model.dart';
import '../models/chat_model.dart';
import '../models/message_model.dart';

class RealtimeDatabaseService {
  static final RealtimeDatabaseService _instance =
      RealtimeDatabaseService._internal();
  factory RealtimeDatabaseService() => _instance;
  RealtimeDatabaseService._internal();

  final FirebaseDatabase _database = FirebaseDatabase.instance;

  // مراجع قاعدة البيانات
  DatabaseReference get _usersRef => _database.ref('users');
  DatabaseReference get _chatsRef => _database.ref('chats');
  DatabaseReference get _messagesRef => _database.ref('messages');

  // ===== إدارة المستخدمين =====

  // حفظ بيانات المستخدم
  Future<void> saveUserData(UserModel user) async {
    try {
      print('🔄 RealtimeDB: Starting to save user data for UID: ${user.uid}');
      print(
        '🔄 RealtimeDB: Database URL: ${_database.app.options.databaseURL}',
      );
      print('🔄 RealtimeDB: User data to save: ${user.toMap()}');

      // التأكد من أن قاعدة البيانات متصلة
      await _database.goOnline();
      print('✅ RealtimeDB: Database is online');

      // حفظ البيانات
      await _usersRef.child(user.uid).set(user.toMap());
      print('✅ RealtimeDB: User data saved successfully');

      // التحقق من الحفظ
      final snapshot = await _usersRef.child(user.uid).get();
      if (snapshot.exists) {
        print('✅ RealtimeDB: Data verification successful');
      } else {
        print('❌ RealtimeDB: Data verification failed');
        throw Exception('فشل في التحقق من حفظ البيانات');
      }
    } catch (e) {
      print('❌ RealtimeDB: Error saving user data: $e');
      print('❌ RealtimeDB: Error type: ${e.runtimeType}');
      throw Exception('خطأ في حفظ بيانات المستخدم: $e');
    }
  }

  // تحديث بيانات المستخدم
  Future<void> updateUserData(String uid, Map<String, dynamic> updates) async {
    try {
      await _usersRef.child(uid).update(updates);
    } catch (e) {
      throw Exception('خطأ في تحديث بيانات المستخدم: $e');
    }
  }

  // الحصول على بيانات المستخدم
  Future<UserModel?> getUserData(String uid) async {
    try {
      final snapshot = await _usersRef.child(uid).get();
      if (snapshot.exists) {
        final data = Map<String, dynamic>.from(snapshot.value as Map);
        return UserModel.fromMap(data);
      }
      return null;
    } catch (e) {
      throw Exception('خطأ في جلب بيانات المستخدم: $e');
    }
  }

  // تحديث حالة الاتصال
  Future<void> updateUserOnlineStatus(String uid, bool isOnline) async {
    try {
      await _usersRef.child(uid).update({
        'isOnline': isOnline,
        'lastSeen': DateTime.now().millisecondsSinceEpoch,
      });
    } catch (e) {
      throw Exception('خطأ في تحديث حالة الاتصال: $e');
    }
  }

  // البحث عن المستخدمين
  Future<List<UserModel>> searchUsers(String query) async {
    try {
      final snapshot = await _usersRef.get();
      if (!snapshot.exists) return [];

      final users = <UserModel>[];
      final data = Map<String, dynamic>.from(snapshot.value as Map);

      data.forEach((key, value) {
        final userData = Map<String, dynamic>.from(value as Map);
        final user = UserModel.fromMap(userData);

        if (user.displayName.toLowerCase().contains(query.toLowerCase()) ||
            user.email.toLowerCase().contains(query.toLowerCase())) {
          users.add(user);
        }
      });

      return users;
    } catch (e) {
      throw Exception('خطأ في البحث عن المستخدمين: $e');
    }
  }

  // الاستماع لتغييرات بيانات المستخدم
  Stream<UserModel?> getUserDataStream(String uid) {
    return _usersRef.child(uid).onValue.map((event) {
      if (event.snapshot.exists) {
        final data = Map<String, dynamic>.from(event.snapshot.value as Map);
        return UserModel.fromMap(data);
      }
      return null;
    });
  }

  // ===== إدارة الشات =====

  // إنشاء أو الحصول على شات
  Future<String> getOrCreateChat(
    String currentUserId,
    String otherUserId,
  ) async {
    try {
      // إنشاء معرف الشات
      List<String> participants = [currentUserId, otherUserId];
      participants.sort();
      String chatId = participants.join('_');

      // التحقق من وجود الشات
      final snapshot = await _chatsRef.child(chatId).get();

      if (!snapshot.exists) {
        // إنشاء شات جديد
        final chatData = {
          'id': chatId,
          'lastActivity': DateTime.now().millisecondsSinceEpoch,
          'createdAt': DateTime.now().millisecondsSinceEpoch,
        };

        // إنشاء الشات
        await _chatsRef.child(chatId).set(chatData);

        // إضافة المشاركين بشكل منفصل
        for (String participantId in participants) {
          await _chatsRef
              .child(chatId)
              .child('participants')
              .child(participantId)
              .set(true);
        }
      }

      return chatId;
    } catch (e) {
      throw Exception('خطأ في إنشاء الشات: $e');
    }
  }

  // إرسال رسالة
  Future<void> sendMessage(String chatId, MessageModel message) async {
    try {
      // حفظ الرسالة
      await _messagesRef.child(chatId).child(message.id).set(message.toMap());

      // تحديث آخر نشاط في الشات
      await _chatsRef.child(chatId).update({
        'lastActivity': DateTime.now().millisecondsSinceEpoch,
        'lastMessage': message.toMap(),
      });
    } catch (e) {
      throw Exception('خطأ في إرسال الرسالة: $e');
    }
  }

  // الحصول على الرسائل
  Stream<List<MessageModel>> getMessages(String chatId) {
    return _messagesRef.child(chatId).orderByChild('timestamp').onValue.map((
      event,
    ) {
      final messages = <MessageModel>[];

      if (event.snapshot.exists) {
        final data = Map<String, dynamic>.from(event.snapshot.value as Map);

        data.forEach((key, value) {
          final messageData = Map<String, dynamic>.from(value as Map);
          messages.add(MessageModel.fromMap(messageData));
        });

        // ترتيب الرسائل حسب الوقت (الأحدث أولاً)
        messages.sort((a, b) => b.timestamp.compareTo(a.timestamp));
      }

      return messages;
    });
  }

  // الحصول على شاتات المستخدم
  Stream<List<ChatModel>> getUserChats(String userId) {
    return _chatsRef.onValue.map((event) {
      final chats = <ChatModel>[];

      if (event.snapshot.exists) {
        final data = Map<String, dynamic>.from(event.snapshot.value as Map);

        data.forEach((key, value) {
          final chatData = Map<String, dynamic>.from(value as Map);

          // التحقق من أن المستخدم جزء من هذا الشات
          if (chatData['participants'] != null) {
            final participantsData = chatData['participants'];

            // إذا كان participants عبارة عن Map (النظام الجديد)
            if (participantsData is Map) {
              final participantsMap = Map<String, dynamic>.from(
                participantsData,
              );
              if (participantsMap.containsKey(userId)) {
                // تحويل participants map إلى list للتوافق مع ChatModel
                final participantsList = participantsMap.keys.toList();
                chatData['participants'] = participantsList;
                chats.add(ChatModel.fromMap(chatData));
              }
            }
            // إذا كان participants عبارة عن List (النظام القديم)
            else if (participantsData is List) {
              final participants = List<String>.from(participantsData);
              if (participants.contains(userId)) {
                chats.add(ChatModel.fromMap(chatData));
              }
            }
          }
        });

        // ترتيب الشاتات حسب آخر نشاط
        chats.sort((a, b) => b.lastActivity.compareTo(a.lastActivity));
      }

      return chats;
    });
  }

  // تحديث حالة الرسالة
  Future<void> updateMessageStatus(
    String chatId,
    String messageId,
    MessageStatus status,
  ) async {
    try {
      await _messagesRef.child(chatId).child(messageId).update({
        'status': status.toString().split('.').last,
      });
    } catch (e) {
      throw Exception('خطأ في تحديث حالة الرسالة: $e');
    }
  }

  // حذف رسالة
  Future<void> deleteMessage(String chatId, String messageId) async {
    try {
      await _messagesRef.child(chatId).child(messageId).remove();
    } catch (e) {
      throw Exception('خطأ في حذف الرسالة: $e');
    }
  }

  // حذف شات
  Future<void> deleteChat(String chatId) async {
    try {
      // حذف جميع الرسائل
      await _messagesRef.child(chatId).remove();
      // حذف الشات
      await _chatsRef.child(chatId).remove();
    } catch (e) {
      throw Exception('خطأ في حذف الشات: $e');
    }
  }

  // ===== وظائف إضافية =====

  // تعيين حالة الاتصال التلقائية
  void setupPresence(String uid) {
    final userStatusRef = _usersRef.child(uid);

    // عند قطع الاتصال، تحديث الحالة إلى غير متصل
    userStatusRef.onDisconnect().update({
      'isOnline': false,
      'lastSeen': DateTime.now().millisecondsSinceEpoch,
    });

    // تحديث الحالة إلى متصل
    userStatusRef.update({
      'isOnline': true,
      'lastSeen': DateTime.now().millisecondsSinceEpoch,
    });
  }

  // إحصائيات المستخدم
  Future<Map<String, int>> getUserStats(String uid) async {
    try {
      final chatsSnapshot = await _chatsRef.get();
      int totalChats = 0;
      int totalMessages = 0;

      if (chatsSnapshot.exists) {
        final chatsData = Map<String, dynamic>.from(chatsSnapshot.value as Map);

        for (final chatData in chatsData.values) {
          bool userInChat = false;

          // التحقق من المشاركين
          if (chatData['participants'] != null) {
            final participantsData = chatData['participants'];

            if (participantsData is Map) {
              final participantsMap = Map<String, dynamic>.from(
                participantsData,
              );
              userInChat = participantsMap.containsKey(uid);
            } else if (participantsData is List) {
              final participants = List<String>.from(participantsData);
              userInChat = participants.contains(uid);
            }
          }

          if (userInChat) {
            totalChats++;

            // عد الرسائل في هذا الشات
            final chatId = chatData['id'];
            final messagesSnapshot = await _messagesRef.child(chatId).get();
            if (messagesSnapshot.exists) {
              final messagesData = Map<String, dynamic>.from(
                messagesSnapshot.value as Map,
              );
              totalMessages += messagesData.length;
            }
          }
        }
      }

      return {'totalChats': totalChats, 'totalMessages': totalMessages};
    } catch (e) {
      throw Exception('خطأ في جلب إحصائيات المستخدم: $e');
    }
  }
}
