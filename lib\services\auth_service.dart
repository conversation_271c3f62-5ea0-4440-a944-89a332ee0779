import 'package:firebase_auth/firebase_auth.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import '../models/user_model.dart';
import 'realtime_database_service.dart';

class AuthService {
  final FirebaseAuth _auth = FirebaseAuth.instance;
  final FirebaseFirestore _firestore = FirebaseFirestore.instance;
  final RealtimeDatabaseService _realtimeDB = RealtimeDatabaseService();

  // Get current user
  User? get currentUser => _auth.currentUser;

  // Auth state changes stream
  Stream<User?> get authStateChanges => _auth.authStateChanges();

  // Sign in with email and password
  Future<UserCredential?> signInWithEmailAndPassword(
    String email,
    String password,
  ) async {
    try {
      UserCredential result = await _auth.signInWithEmailAndPassword(
        email: email,
        password: password,
      );

      // Update user online status
      if (result.user != null) {
        await _realtimeDB.updateUserOnlineStatus(result.user!.uid, true);
        // Setup presence for automatic status updates
        _realtimeDB.setupPresence(result.user!.uid);
      }

      return result;
    } on FirebaseAuthException catch (e) {
      throw _handleAuthException(e);
    }
  }

  // Register with email and password
  Future<UserCredential?> registerWithEmailAndPassword(
    String email,
    String password,
    String displayName, {
    String? phoneNumber,
    int? age,
  }) async {
    try {
      UserCredential result = await _auth.createUserWithEmailAndPassword(
        email: email,
        password: password,
      );

      if (result.user != null) {
        // Update display name
        await result.user!.updateDisplayName(displayName);

        // Create user document in both Firestore and Realtime Database
        UserModel userModel = UserModel(
          uid: result.user!.uid,
          email: email,
          displayName: displayName,
          createdAt: DateTime.now(),
          lastSeen: DateTime.now(),
          isOnline: true,
          phoneNumber: phoneNumber,
          age: age,
          status: 'متاح', // حالة افتراضية
        );

        try {
          // Save to Firestore (for compatibility)
          await _firestore
              .collection('users')
              .doc(result.user!.uid)
              .set(userModel.toMap());
          print('✅ User saved to Firestore successfully');
        } catch (e) {
          print('❌ Error saving to Firestore: $e');
        }

        try {
          // Save to Realtime Database with detailed logging
          print('🔄 Attempting to save user to Realtime Database...');
          print('📊 User data: ${userModel.toMap()}');

          await _realtimeDB.saveUserData(userModel);
          print('✅ User saved to Realtime Database successfully');

          // Setup presence for automatic status updates
          print('🔄 Setting up presence...');
          _realtimeDB.setupPresence(result.user!.uid);
          print('✅ Presence setup completed');

          // Verify data was saved
          print('🔍 Verifying data was saved...');
          final savedUser = await _realtimeDB.getUserData(result.user!.uid);
          if (savedUser != null) {
            print('✅ Data verification successful: ${savedUser.displayName}');
          } else {
            print('❌ Data verification failed - user not found in database');
          }
        } catch (e) {
          print('❌ Error saving to Realtime Database: $e');
          print('❌ Error type: ${e.runtimeType}');
          print('❌ Stack trace: ${StackTrace.current}');

          // رمي الاستثناء لإيقاف العملية والإبلاغ عن الخطأ
          throw Exception('فشل في حفظ البيانات في قاعدة البيانات: $e');
        }
      }

      return result;
    } on FirebaseAuthException catch (e) {
      throw _handleAuthException(e);
    }
  }

  // Sign out
  Future<void> signOut() async {
    if (currentUser != null) {
      await _realtimeDB.updateUserOnlineStatus(currentUser!.uid, false);
    }
    await _auth.signOut();
  }

  // Update user online status
  Future<void> updateUserOnlineStatus(String uid, bool isOnline) async {
    try {
      // Update in both Firestore and Realtime Database
      await _firestore.collection('users').doc(uid).update({
        'isOnline': isOnline,
        'lastSeen': DateTime.now().millisecondsSinceEpoch,
      });

      await _realtimeDB.updateUserOnlineStatus(uid, isOnline);
    } catch (e) {
      print('Error updating online status: $e');
    }
  }

  // Get user data
  Future<UserModel?> getUserData(String uid) async {
    try {
      // Try to get from Realtime Database first
      UserModel? user = await _realtimeDB.getUserData(uid);
      if (user != null) {
        return user;
      }

      // Fallback to Firestore
      DocumentSnapshot doc =
          await _firestore.collection('users').doc(uid).get();
      if (doc.exists) {
        return UserModel.fromMap(doc.data() as Map<String, dynamic>);
      }
      return null;
    } catch (e) {
      print('Error getting user data: $e');
      return null;
    }
  }

  // Search users by email or name
  Future<List<UserModel>> searchUsersByEmail(String query) async {
    try {
      // Search in Realtime Database first
      List<UserModel> users = await _realtimeDB.searchUsers(query);
      if (users.isNotEmpty) {
        return users;
      }

      // Fallback to Firestore search by email
      QuerySnapshot firestoreQuery =
          await _firestore
              .collection('users')
              .where('email', isEqualTo: query)
              .limit(10)
              .get();

      return firestoreQuery.docs
          .map((doc) => UserModel.fromMap(doc.data() as Map<String, dynamic>))
          .toList();
    } catch (e) {
      print('Error searching users: $e');
      return [];
    }
  }

  // Add method to update user profile with additional info
  Future<void> updateUserProfile(
    String uid,
    Map<String, dynamic> updates,
  ) async {
    try {
      // Update in both Firestore and Realtime Database
      await _firestore.collection('users').doc(uid).update(updates);
      await _realtimeDB.updateUserData(uid, updates);
    } catch (e) {
      throw Exception('خطأ في تحديث الملف الشخصي: $e');
    }
  }

  // Get user data stream from Realtime Database
  Stream<UserModel?> getUserDataStream(String uid) {
    return _realtimeDB.getUserDataStream(uid);
  }

  // Handle Firebase Auth exceptions
  String _handleAuthException(FirebaseAuthException e) {
    switch (e.code) {
      case 'user-not-found':
        return 'لا يوجد مستخدم بهذا البريد الإلكتروني';
      case 'wrong-password':
        return 'كلمة المرور غير صحيحة';
      case 'email-already-in-use':
        return 'البريد الإلكتروني مستخدم بالفعل';
      case 'weak-password':
        return 'كلمة المرور ضعيفة جداً';
      case 'invalid-email':
        return 'البريد الإلكتروني غير صحيح';
      default:
        return 'حدث خطأ: ${e.message}';
    }
  }
}
