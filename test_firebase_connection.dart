import 'package:firebase_core/firebase_core.dart';
import 'package:firebase_database/firebase_database.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'firebase_options.dart';

void main() async {
  print('🔄 بدء اختبار الاتصال بـ Firebase...');
  
  try {
    // تهيئة Firebase
    await Firebase.initializeApp(
      options: DefaultFirebaseOptions.currentPlatform,
    );
    print('✅ تم تهيئة Firebase بنجاح');

    // اختبار الاتصال بـ Realtime Database
    final database = FirebaseDatabase.instance;
    print('📡 رابط قاعدة البيانات: ${database.app.options.databaseURL}');

    // اختبار كتابة بسيطة
    final testRef = database.ref('test');
    await testRef.set({
      'timestamp': DateTime.now().millisecondsSinceEpoch,
      'message': 'اختبار الاتصال',
    });
    print('✅ تم اختبار الكتابة بنجاح');

    // اختبار القراءة
    final snapshot = await testRef.get();
    if (snapshot.exists) {
      print('✅ تم اختبار القراءة بنجاح');
      print('📄 البيانات: ${snapshot.value}');
    } else {
      print('❌ فشل في قراءة البيانات');
    }

    // حذف بيانات الاختبار
    await testRef.remove();
    print('🗑️ تم حذف بيانات الاختبار');

    print('🎉 جميع الاختبارات نجحت!');
    
  } catch (e) {
    print('❌ خطأ في الاختبار: $e');
    
    if (e.toString().contains('Permission denied')) {
      print('');
      print('🚨 المشكلة: قواعد الأمان في Firebase مقيدة');
      print('');
      print('🔧 الحل:');
      print('1. اذهب إلى Firebase Console');
      print('2. افتح Realtime Database → Rules');
      print('3. استبدل القواعد بهذه:');
      print('');
      print('{');
      print('  "rules": {');
      print('    ".read": "auth != null",');
      print('    ".write": "auth != null"');
      print('  }');
      print('}');
      print('');
      print('4. انقر Publish');
    }
  }
}
