import 'package:flutter/material.dart';
import 'package:firebase_auth/firebase_auth.dart';
import '../services/auth_service.dart';
import '../models/user_model.dart';

class AuthProvider with ChangeNotifier {
  final AuthService _authService = AuthService();

  User? _user;
  UserModel? _userModel;
  bool _isLoading = false;
  String? _errorMessage;

  User? get user => _user;
  UserModel? get userModel => _userModel;
  bool get isLoading => _isLoading;
  String? get errorMessage => _errorMessage;
  bool get isAuthenticated => _user != null;

  AuthProvider() {
    // Listen to auth state changes
    _authService.authStateChanges.listen((User? user) async {
      _user = user;
      if (user != null) {
        _userModel = await _authService.getUserData(user.uid);
      } else {
        _userModel = null;
      }
      notifyListeners();
    });
  }

  // Sign in
  Future<bool> signIn(String email, String password) async {
    try {
      _setLoading(true);
      _clearError();

      UserCredential? result = await _authService.signInWithEmailAndPassword(
        email,
        password,
      );

      if (result != null) {
        _userModel = await _authService.getUserData(result.user!.uid);
        _setLoading(false);
        return true;
      }

      _setLoading(false);
      return false;
    } catch (e) {
      _setError(e.toString());
      _setLoading(false);
      return false;
    }
  }

  // Register
  Future<bool> register(
    String email,
    String password,
    String displayName, {
    String? phoneNumber,
    int? age,
  }) async {
    try {
      _setLoading(true);
      _clearError();

      // بدء عملية التسجيل

      UserCredential? result = await _authService.registerWithEmailAndPassword(
        email,
        password,
        displayName,
        phoneNumber: phoneNumber,
        age: age,
      );

      if (result != null) {
        _user = result.user;
        _userModel = await _authService.getUserData(result.user!.uid);
        _setLoading(false);
        return true;
      }

      _setError('فشل في إنشاء الحساب');
      _setLoading(false);
      return false;
    } catch (e) {
      _setError('خطأ في إنشاء الحساب: ${e.toString()}');
      _setLoading(false);
      return false;
    }
  }

  // Sign out
  Future<void> signOut() async {
    try {
      _setLoading(true);
      await _authService.signOut();
      _user = null;
      _userModel = null;
      _setLoading(false);
    } catch (e) {
      _setError(e.toString());
      _setLoading(false);
    }
  }

  // Search users
  Future<List<UserModel>> searchUsers(String email) async {
    try {
      return await _authService.searchUsersByEmail(email);
    } catch (e) {
      _setError(e.toString());
      return [];
    }
  }

  // Update online status
  Future<void> updateOnlineStatus(bool isOnline) async {
    if (_user != null) {
      await _authService.updateUserOnlineStatus(_user!.uid, isOnline);
    }
  }

  void _setLoading(bool loading) {
    _isLoading = loading;
    notifyListeners();
  }

  void _setError(String error) {
    _errorMessage = error;
    notifyListeners();
  }

  void _clearError() {
    _errorMessage = null;
    notifyListeners();
  }

  void clearError() {
    _clearError();
  }
}
