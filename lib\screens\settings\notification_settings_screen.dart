import 'package:flutter/material.dart';
import 'package:shared_preferences/shared_preferences.dart';

class NotificationSettingsScreen extends StatefulWidget {
  const NotificationSettingsScreen({super.key});

  @override
  State<NotificationSettingsScreen> createState() => _NotificationSettingsScreenState();
}

class _NotificationSettingsScreenState extends State<NotificationSettingsScreen> {
  bool _messageNotifications = true;
  bool _soundEnabled = true;
  bool _vibrationEnabled = true;
  bool _showPreview = true;
  bool _groupNotifications = false;
  String _selectedSound = 'افتراضي';
  
  final List<String> _soundOptions = [
    'افتراضي',
    'نغمة 1',
    'نغمة 2',
    'نغمة 3',
    'صامت',
  ];

  @override
  void initState() {
    super.initState();
    _loadSettings();
  }

  Future<void> _loadSettings() async {
    final prefs = await SharedPreferences.getInstance();
    setState(() {
      _messageNotifications = prefs.getBool('message_notifications') ?? true;
      _soundEnabled = prefs.getBool('sound_enabled') ?? true;
      _vibrationEnabled = prefs.getBool('vibration_enabled') ?? true;
      _showPreview = prefs.getBool('show_preview') ?? true;
      _groupNotifications = prefs.getBool('group_notifications') ?? false;
      _selectedSound = prefs.getString('selected_sound') ?? 'افتراضي';
    });
  }

  Future<void> _saveSettings() async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.setBool('message_notifications', _messageNotifications);
    await prefs.setBool('sound_enabled', _soundEnabled);
    await prefs.setBool('vibration_enabled', _vibrationEnabled);
    await prefs.setBool('show_preview', _showPreview);
    await prefs.setBool('group_notifications', _groupNotifications);
    await prefs.setString('selected_sound', _selectedSound);
    
    if (mounted) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('تم حفظ الإعدادات بنجاح'),
          backgroundColor: Colors.green,
        ),
      );
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.black,
      appBar: AppBar(
        title: const Text('إعدادات الإشعارات'),
        backgroundColor: Colors.black,
        foregroundColor: Colors.white,
        elevation: 0,
        actions: [
          TextButton(
            onPressed: _saveSettings,
            child: const Text(
              'حفظ',
              style: TextStyle(
                color: Colors.white,
                fontWeight: FontWeight.bold,
              ),
            ),
          ),
        ],
      ),
      body: ListView(
        padding: const EdgeInsets.all(16),
        children: [
          // Header
          Container(
            padding: const EdgeInsets.all(20),
            decoration: BoxDecoration(
              gradient: const LinearGradient(
                colors: [Colors.blue, Colors.purple],
                begin: Alignment.topLeft,
                end: Alignment.bottomRight,
              ),
              borderRadius: BorderRadius.circular(16),
            ),
            child: const Column(
              children: [
                Icon(
                  Icons.notifications,
                  size: 48,
                  color: Colors.white,
                ),
                SizedBox(height: 16),
                Text(
                  'إعدادات الإشعارات',
                  style: TextStyle(
                    fontSize: 20,
                    fontWeight: FontWeight.bold,
                    color: Colors.white,
                  ),
                ),
                SizedBox(height: 8),
                Text(
                  'تخصيص كيفية تلقي الإشعارات',
                  style: TextStyle(
                    fontSize: 14,
                    color: Colors.white70,
                  ),
                ),
              ],
            ),
          ),
          const SizedBox(height: 24),

          // General Settings
          _buildSectionHeader('الإعدادات العامة'),
          _buildSettingsCard([
            _buildSwitchTile(
              icon: Icons.notifications_active,
              title: 'إشعارات الرسائل',
              subtitle: 'تلقي إشعارات عند وصول رسائل جديدة',
              value: _messageNotifications,
              onChanged: (value) {
                setState(() {
                  _messageNotifications = value;
                });
              },
            ),
            const Divider(color: Colors.grey),
            _buildSwitchTile(
              icon: Icons.preview,
              title: 'معاينة الرسائل',
              subtitle: 'إظهار محتوى الرسالة في الإشعار',
              value: _showPreview,
              onChanged: (value) {
                setState(() {
                  _showPreview = value;
                });
              },
            ),
            const Divider(color: Colors.grey),
            _buildSwitchTile(
              icon: Icons.group_work,
              title: 'تجميع الإشعارات',
              subtitle: 'تجميع إشعارات المحادثة الواحدة',
              value: _groupNotifications,
              onChanged: (value) {
                setState(() {
                  _groupNotifications = value;
                });
              },
            ),
          ]),
          const SizedBox(height: 24),

          // Sound & Vibration
          _buildSectionHeader('الصوت والاهتزاز'),
          _buildSettingsCard([
            _buildSwitchTile(
              icon: Icons.volume_up,
              title: 'تفعيل الصوت',
              subtitle: 'تشغيل صوت عند وصول الإشعارات',
              value: _soundEnabled,
              onChanged: (value) {
                setState(() {
                  _soundEnabled = value;
                });
              },
            ),
            const Divider(color: Colors.grey),
            ListTile(
              leading: const Icon(Icons.music_note, color: Colors.white),
              title: const Text(
                'نغمة الإشعار',
                style: TextStyle(color: Colors.white),
              ),
              subtitle: Text(
                _selectedSound,
                style: const TextStyle(color: Colors.white70),
              ),
              trailing: const Icon(Icons.arrow_forward_ios, color: Colors.white54),
              onTap: _soundEnabled ? _showSoundPicker : null,
            ),
            const Divider(color: Colors.grey),
            _buildSwitchTile(
              icon: Icons.vibration,
              title: 'الاهتزاز',
              subtitle: 'اهتزاز الجهاز عند وصول الإشعارات',
              value: _vibrationEnabled,
              onChanged: (value) {
                setState(() {
                  _vibrationEnabled = value;
                });
              },
            ),
          ]),
          const SizedBox(height: 24),

          // Test Notification
          _buildSectionHeader('اختبار'),
          _buildSettingsCard([
            ListTile(
              leading: const Icon(Icons.send, color: Colors.blue),
              title: const Text(
                'إرسال إشعار تجريبي',
                style: TextStyle(color: Colors.white),
              ),
              subtitle: const Text(
                'اختبر إعدادات الإشعارات الحالية',
                style: TextStyle(color: Colors.white70),
              ),
              trailing: const Icon(Icons.arrow_forward_ios, color: Colors.white54),
              onTap: _sendTestNotification,
            ),
          ]),
          const SizedBox(height: 32),

          // Reset Button
          SizedBox(
            width: double.infinity,
            child: OutlinedButton.icon(
              onPressed: _resetToDefaults,
              icon: const Icon(Icons.restore),
              label: const Text('إعادة تعيين للافتراضي'),
              style: OutlinedButton.styleFrom(
                foregroundColor: Colors.white,
                side: const BorderSide(color: Colors.white54),
                padding: const EdgeInsets.symmetric(vertical: 16),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(8),
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildSectionHeader(String title) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 12),
      child: Text(
        title,
        style: const TextStyle(
          fontSize: 18,
          fontWeight: FontWeight.bold,
          color: Colors.white,
        ),
      ),
    );
  }

  Widget _buildSettingsCard(List<Widget> children) {
    return Container(
      decoration: BoxDecoration(
        color: Colors.grey[900],
        borderRadius: BorderRadius.circular(12),
      ),
      child: Column(children: children),
    );
  }

  Widget _buildSwitchTile({
    required IconData icon,
    required String title,
    required String subtitle,
    required bool value,
    required ValueChanged<bool> onChanged,
  }) {
    return ListTile(
      leading: Icon(icon, color: Colors.white),
      title: Text(
        title,
        style: const TextStyle(color: Colors.white),
      ),
      subtitle: Text(
        subtitle,
        style: const TextStyle(color: Colors.white70),
      ),
      trailing: Switch(
        value: value,
        onChanged: onChanged,
        activeColor: Colors.blue,
      ),
    );
  }

  void _showSoundPicker() {
    showModalBottomSheet(
      context: context,
      backgroundColor: Colors.grey[900],
      shape: const RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
      ),
      builder: (context) {
        return Container(
          padding: const EdgeInsets.all(20),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              const Text(
                'اختر نغمة الإشعار',
                style: TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                  color: Colors.white,
                ),
              ),
              const SizedBox(height: 20),
              ...(_soundOptions.map((sound) => ListTile(
                title: Text(
                  sound,
                  style: const TextStyle(color: Colors.white),
                ),
                leading: Radio<String>(
                  value: sound,
                  groupValue: _selectedSound,
                  onChanged: (value) {
                    setState(() {
                      _selectedSound = value!;
                    });
                    Navigator.pop(context);
                  },
                  activeColor: Colors.blue,
                ),
              ))),
            ],
          ),
        );
      },
    );
  }

  void _sendTestNotification() {
    // TODO: Implement test notification
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('تم إرسال إشعار تجريبي'),
        backgroundColor: Colors.blue,
      ),
    );
  }

  void _resetToDefaults() {
    setState(() {
      _messageNotifications = true;
      _soundEnabled = true;
      _vibrationEnabled = true;
      _showPreview = true;
      _groupNotifications = false;
      _selectedSound = 'افتراضي';
    });
    
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('تم إعادة تعيين الإعدادات للافتراضي'),
        backgroundColor: Colors.green,
      ),
    );
  }
}
