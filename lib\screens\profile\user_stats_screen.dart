import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../../providers/auth_provider.dart';
import '../../services/realtime_database_service.dart';

class UserStatsScreen extends StatefulWidget {
  const UserStatsScreen({super.key});

  @override
  State<UserStatsScreen> createState() => _UserStatsScreenState();
}

class _UserStatsScreenState extends State<UserStatsScreen> {
  final RealtimeDatabaseService _realtimeDB = RealtimeDatabaseService();
  Map<String, int>? _stats;
  bool _isLoading = true;

  @override
  void initState() {
    super.initState();
    _loadStats();
  }

  Future<void> _loadStats() async {
    try {
      final authProvider = Provider.of<AuthProvider>(context, listen: false);
      if (authProvider.user != null) {
        final stats = await _realtimeDB.getUserStats(authProvider.user!.uid);
        setState(() {
          _stats = stats;
          _isLoading = false;
        });
      }
    } catch (e) {
      setState(() {
        _isLoading = false;
      });
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('خطأ في تحميل الإحصائيات: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.black,
      appBar: AppBar(
        title: const Text('إحصائياتي'),
        backgroundColor: Colors.black,
        foregroundColor: Colors.white,
        elevation: 0,
      ),
      body: _isLoading
          ? const Center(
              child: CircularProgressIndicator(color: Colors.white),
            )
          : _stats == null
              ? const Center(
                  child: Text(
                    'فشل في تحميل الإحصائيات',
                    style: TextStyle(color: Colors.white),
                  ),
                )
              : Padding(
                  padding: const EdgeInsets.all(16),
                  child: Column(
                    children: [
                      // Header
                      Container(
                        width: double.infinity,
                        padding: const EdgeInsets.all(24),
                        decoration: BoxDecoration(
                          gradient: const LinearGradient(
                            colors: [Colors.blue, Colors.purple],
                            begin: Alignment.topLeft,
                            end: Alignment.bottomRight,
                          ),
                          borderRadius: BorderRadius.circular(16),
                        ),
                        child: Column(
                          children: [
                            const Icon(
                              Icons.analytics,
                              size: 48,
                              color: Colors.white,
                            ),
                            const SizedBox(height: 16),
                            const Text(
                              'إحصائياتك في التطبيق',
                              style: TextStyle(
                                fontSize: 20,
                                fontWeight: FontWeight.bold,
                                color: Colors.white,
                              ),
                            ),
                            const SizedBox(height: 8),
                            Consumer<AuthProvider>(
                              builder: (context, authProvider, child) {
                                return Text(
                                  'مرحباً ${authProvider.userModel?.displayName ?? ""}',
                                  style: const TextStyle(
                                    fontSize: 16,
                                    color: Colors.white70,
                                  ),
                                );
                              },
                            ),
                          ],
                        ),
                      ),
                      const SizedBox(height: 32),

                      // Stats Cards
                      Expanded(
                        child: GridView.count(
                          crossAxisCount: 2,
                          crossAxisSpacing: 16,
                          mainAxisSpacing: 16,
                          children: [
                            _buildStatCard(
                              icon: Icons.chat_bubble_outline,
                              title: 'إجمالي المحادثات',
                              value: '${_stats!['totalChats'] ?? 0}',
                              color: Colors.green,
                            ),
                            _buildStatCard(
                              icon: Icons.message,
                              title: 'إجمالي الرسائل',
                              value: '${_stats!['totalMessages'] ?? 0}',
                              color: Colors.orange,
                            ),
                            _buildStatCard(
                              icon: Icons.schedule,
                              title: 'متوسط الرسائل يومياً',
                              value: _calculateDailyAverage(),
                              color: Colors.purple,
                            ),
                            _buildStatCard(
                              icon: Icons.trending_up,
                              title: 'نشاطك',
                              value: _getActivityLevel(),
                              color: Colors.blue,
                            ),
                          ],
                        ),
                      ),

                      // Additional Info
                      Container(
                        width: double.infinity,
                        padding: const EdgeInsets.all(16),
                        decoration: BoxDecoration(
                          color: Colors.grey[900],
                          borderRadius: BorderRadius.circular(12),
                        ),
                        child: Column(
                          children: [
                            const Text(
                              'معلومات إضافية',
                              style: TextStyle(
                                fontSize: 18,
                                fontWeight: FontWeight.bold,
                                color: Colors.white,
                              ),
                            ),
                            const SizedBox(height: 16),
                            Consumer<AuthProvider>(
                              builder: (context, authProvider, child) {
                                final user = authProvider.userModel;
                                if (user == null) return const SizedBox();
                                
                                return Column(
                                  children: [
                                    _buildInfoRow(
                                      'تاريخ الانضمام',
                                      _formatDate(user.createdAt),
                                    ),
                                    const SizedBox(height: 8),
                                    _buildInfoRow(
                                      'آخر ظهور',
                                      _formatDate(user.lastSeen),
                                    ),
                                    const SizedBox(height: 8),
                                    _buildInfoRow(
                                      'الحالة الحالية',
                                      user.status ?? 'غير محدد',
                                    ),
                                  ],
                                );
                              },
                            ),
                          ],
                        ),
                      ),
                      const SizedBox(height: 16),

                      // Refresh Button
                      SizedBox(
                        width: double.infinity,
                        child: ElevatedButton.icon(
                          onPressed: () {
                            setState(() {
                              _isLoading = true;
                            });
                            _loadStats();
                          },
                          icon: const Icon(Icons.refresh),
                          label: const Text('تحديث الإحصائيات'),
                          style: ElevatedButton.styleFrom(
                            backgroundColor: Colors.white,
                            foregroundColor: Colors.black,
                            padding: const EdgeInsets.symmetric(vertical: 16),
                            shape: RoundedRectangleBorder(
                              borderRadius: BorderRadius.circular(8),
                            ),
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
    );
  }

  Widget _buildStatCard({
    required IconData icon,
    required String title,
    required String value,
    required Color color,
  }) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.grey[900],
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: color.withOpacity(0.3)),
      ),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            icon,
            size: 32,
            color: color,
          ),
          const SizedBox(height: 12),
          Text(
            value,
            style: TextStyle(
              fontSize: 24,
              fontWeight: FontWeight.bold,
              color: color,
            ),
          ),
          const SizedBox(height: 8),
          Text(
            title,
            textAlign: TextAlign.center,
            style: const TextStyle(
              fontSize: 12,
              color: Colors.white70,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildInfoRow(String label, String value) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        Text(
          label,
          style: const TextStyle(
            color: Colors.white70,
            fontSize: 14,
          ),
        ),
        Text(
          value,
          style: const TextStyle(
            color: Colors.white,
            fontSize: 14,
            fontWeight: FontWeight.w500,
          ),
        ),
      ],
    );
  }

  String _calculateDailyAverage() {
    if (_stats == null) return '0';
    
    final totalMessages = _stats!['totalMessages'] ?? 0;
    final authProvider = Provider.of<AuthProvider>(context, listen: false);
    final user = authProvider.userModel;
    
    if (user == null) return '0';
    
    final daysSinceJoined = DateTime.now().difference(user.createdAt).inDays;
    if (daysSinceJoined == 0) return totalMessages.toString();
    
    final average = (totalMessages / daysSinceJoined).round();
    return average.toString();
  }

  String _getActivityLevel() {
    if (_stats == null) return 'منخفض';
    
    final totalMessages = _stats!['totalMessages'] ?? 0;
    final totalChats = _stats!['totalChats'] ?? 0;
    
    if (totalMessages > 100 && totalChats > 10) {
      return 'عالي';
    } else if (totalMessages > 50 && totalChats > 5) {
      return 'متوسط';
    } else {
      return 'منخفض';
    }
  }

  String _formatDate(DateTime date) {
    final now = DateTime.now();
    final difference = now.difference(date);
    
    if (difference.inDays == 0) {
      return 'اليوم';
    } else if (difference.inDays == 1) {
      return 'أمس';
    } else if (difference.inDays < 7) {
      return 'منذ ${difference.inDays} أيام';
    } else if (difference.inDays < 30) {
      return 'منذ ${(difference.inDays / 7).round()} أسابيع';
    } else if (difference.inDays < 365) {
      return 'منذ ${(difference.inDays / 30).round()} شهور';
    } else {
      return 'منذ ${(difference.inDays / 365).round()} سنوات';
    }
  }
}
