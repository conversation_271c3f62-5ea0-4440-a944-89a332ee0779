import 'package:firebase_database/firebase_database.dart';
import 'package:firebase_auth/firebase_auth.dart';

class FirebaseRulesTest {
  static final FirebaseDatabase _database = FirebaseDatabase.instance;
  static final FirebaseAuth _auth = FirebaseAuth.instance;

  /// اختبار سريع لقواعد Firebase
  static Future<String> testRules() async {
    final results = <String>[];
    
    try {
      final currentUser = _auth.currentUser;
      
      if (currentUser == null) {
        return '❌ لا يوجد مستخدم مسجل دخول';
      }

      results.add('✅ المستخدم مسجل دخول: ${currentUser.email}');
      results.add('🔑 UID: ${currentUser.uid}');

      // اختبار الاتصال بقاعدة البيانات
      try {
        await _database.goOnline();
        results.add('✅ الاتصال بقاعدة البيانات نجح');
      } catch (e) {
        results.add('❌ فشل الاتصال بقاعدة البيانات: $e');
        return results.join('\n');
      }

      // اختبار القراءة من users
      try {
        final usersRef = _database.ref('users');
        final snapshot = await usersRef.limitToFirst(1).get();
        results.add('✅ قراءة users نجحت');
      } catch (e) {
        results.add('❌ فشل قراءة users: $e');
      }

      // اختبار الكتابة في users
      try {
        final testRef = _database.ref('users').child('test_${DateTime.now().millisecondsSinceEpoch}');
        await testRef.set({'test': true, 'timestamp': DateTime.now().millisecondsSinceEpoch});
        results.add('✅ كتابة users نجحت');
        
        // حذف البيانات التجريبية
        await testRef.remove();
        results.add('✅ حذف البيانات التجريبية نجح');
      } catch (e) {
        results.add('❌ فشل كتابة users: $e');
      }

      // اختبار كتابة بيانات المستخدم الحالي
      try {
        final userRef = _database.ref('users').child(currentUser.uid);
        final testData = {
          'uid': currentUser.uid,
          'email': currentUser.email,
          'displayName': currentUser.displayName ?? 'Test User',
          'test': true,
          'timestamp': DateTime.now().millisecondsSinceEpoch,
        };
        
        await userRef.set(testData);
        results.add('✅ كتابة بيانات المستخدم الحالي نجحت');
        
        // التحقق من الحفظ
        final snapshot = await userRef.get();
        if (snapshot.exists) {
          results.add('✅ التحقق من حفظ البيانات نجح');
        } else {
          results.add('❌ فشل التحقق من حفظ البيانات');
        }
      } catch (e) {
        results.add('❌ فشل كتابة بيانات المستخدم: $e');
      }

    } catch (e) {
      results.add('❌ خطأ عام: $e');
    }

    return results.join('\n');
  }

  /// اختبار قواعد الأمان المحددة
  static Future<String> testSpecificRules() async {
    final results = <String>[];
    
    try {
      final currentUser = _auth.currentUser;
      
      if (currentUser == null) {
        return '❌ لا يوجد مستخدم مسجل دخول';
      }

      // اختبار القواعد المختلفة
      final testCases = [
        {
          'name': 'قراءة جميع المستخدمين',
          'path': 'users',
          'operation': 'read',
        },
        {
          'name': 'كتابة في مجلد المستخدم الحالي',
          'path': 'users/${currentUser.uid}',
          'operation': 'write',
        },
        {
          'name': 'كتابة في مجلد مستخدم آخر',
          'path': 'users/other_user_id',
          'operation': 'write',
        },
        {
          'name': 'قراءة الرسائل',
          'path': 'messages',
          'operation': 'read',
        },
        {
          'name': 'كتابة الرسائل',
          'path': 'messages',
          'operation': 'write',
        },
      ];

      for (final testCase in testCases) {
        try {
          final ref = _database.ref(testCase['path'] as String);
          
          if (testCase['operation'] == 'read') {
            await ref.limitToFirst(1).get();
            results.add('✅ ${testCase['name']}: مسموح');
          } else if (testCase['operation'] == 'write') {
            final testRef = ref.child('test_${DateTime.now().millisecondsSinceEpoch}');
            await testRef.set({'test': true});
            await testRef.remove();
            results.add('✅ ${testCase['name']}: مسموح');
          }
        } catch (e) {
          results.add('❌ ${testCase['name']}: ممنوع - $e');
        }
      }

    } catch (e) {
      results.add('❌ خطأ في اختبار القواعد: $e');
    }

    return results.join('\n');
  }

  /// اختبار شامل مع تفاصيل
  static Future<Map<String, dynamic>> comprehensiveTest() async {
    final result = <String, dynamic>{};
    
    try {
      // معلومات المستخدم
      final currentUser = _auth.currentUser;
      result['user_info'] = {
        'authenticated': currentUser != null,
        'uid': currentUser?.uid,
        'email': currentUser?.email,
        'display_name': currentUser?.displayName,
      };

      // معلومات قاعدة البيانات
      result['database_info'] = {
        'url': _database.app.options.databaseURL,
        'project_id': _database.app.options.projectId,
      };

      // اختبار الاتصال
      try {
        await _database.goOnline();
        result['connection_test'] = {'status': 'success'};
      } catch (e) {
        result['connection_test'] = {'status': 'failed', 'error': e.toString()};
      }

      // اختبار القواعد
      if (currentUser != null) {
        result['rules_test'] = await _testRulesDetailed(currentUser);
      }

      result['timestamp'] = DateTime.now().toIso8601String();
      result['overall_status'] = 'completed';

    } catch (e) {
      result['overall_status'] = 'failed';
      result['error'] = e.toString();
    }

    return result;
  }

  static Future<Map<String, dynamic>> _testRulesDetailed(User user) async {
    final rulesTest = <String, dynamic>{};

    // اختبار قراءة users
    try {
      final usersRef = _database.ref('users');
      await usersRef.limitToFirst(1).get();
      rulesTest['users_read'] = {'status': 'allowed'};
    } catch (e) {
      rulesTest['users_read'] = {'status': 'denied', 'error': e.toString()};
    }

    // اختبار كتابة users
    try {
      final testRef = _database.ref('users').child('test_${DateTime.now().millisecondsSinceEpoch}');
      await testRef.set({'test': true});
      await testRef.remove();
      rulesTest['users_write'] = {'status': 'allowed'};
    } catch (e) {
      rulesTest['users_write'] = {'status': 'denied', 'error': e.toString()};
    }

    // اختبار كتابة بيانات المستخدم الحالي
    try {
      final userRef = _database.ref('users').child(user.uid);
      await userRef.update({'last_test': DateTime.now().millisecondsSinceEpoch});
      rulesTest['own_user_write'] = {'status': 'allowed'};
    } catch (e) {
      rulesTest['own_user_write'] = {'status': 'denied', 'error': e.toString()};
    }

    return rulesTest;
  }
}
