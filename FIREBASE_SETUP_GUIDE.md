# دليل إعداد Firebase Realtime Database

## المشكلة الحالية
عند إنشاء حساب جديد، يتم تسجيل المستخدم في Firebase Authentication بنجاح، لكن لا يتم حفظ البيانات الإضافية في Realtime Database بسبب قواعد الأمان المقيدة.

## الحل السريع

### 1. تحديث قواعد Realtime Database

اذهب إلى Firebase Console:
1. افتح مشروع Firebase الخاص بك
2. اذهب إلى **Realtime Database**
3. انقر على تبويب **Rules**
4. استبدل القواعد الحالية بالقواعد التالية:

#### للاختبار (مؤقت - ستظهر رسالة تحذير):
```json
{
  "rules": {
    ".read": "auth != null",
    ".write": "auth != null"
  }
}
```

#### للإنتاج (آمن ومُحسن):
```json
{
  "rules": {
    "users": {
      "$uid": {
        ".read": "$uid === auth.uid",
        ".write": "$uid === auth.uid",
        ".validate": "newData.hasChildren(['uid', 'email', 'displayName', 'createdAt', 'lastSeen', 'isOnline'])"
      }
    },
    "chats": {
      "$chatId": {
        ".read": "root.child('chats').child($chatId).child('participants').child(auth.uid).exists()",
        ".write": "root.child('chats').child($chatId).child('participants').child(auth.uid).exists()",
        "participants": {
          "$participantId": {
            ".validate": "$participantId === auth.uid || root.child('chats').child($chatId).child('participants').child(auth.uid).exists()"
          }
        }
      }
    },
    "messages": {
      "$chatId": {
        ".read": "root.child('chats').child($chatId).child('participants').child(auth.uid).exists()",
        ".write": "root.child('chats').child($chatId).child('participants').child(auth.uid).exists()",
        "$messageId": {
          ".validate": "newData.child('senderId').val() === auth.uid"
        }
      }
    },
    ".read": false,
    ".write": false
  }
}
```

5. انقر على **Publish** لحفظ القواعد

### 🚨 حول رسالة التحذير

عند استخدام قواعد الاختبار، ستظهر رسالة:
> "قواعد الأمان لديك غير آمنة. يمكن لأي مستخدم مُصادق عليه سرقة أو تعديل أو حذف بيانات من قاعدة بياناتك."

**هذا طبيعي!** هذه القواعد للاختبار فقط. بمجرد التأكد من عمل التطبيق:
1. استبدل القواعد بقواعد الإنتاج الآمنة (أعلاه)
2. ستختفي رسالة التحذير
3. ستكون بياناتك محمية بشكل كامل

### 2. التحقق من إعداد Realtime Database

تأكد من أن:
1. **Database URL** صحيح في `firebase_options.dart`:
   ```
   https://chat-app-f404c-default-rtdb.firebaseio.com
   ```

2. **Region** صحيح (يجب أن يكون us-central1 أو المنطقة التي اخترتها)

### 3. اختبار الإعداد

1. شغل التطبيق: `flutter run`
2. اذهب إلى الملف الشخصي
3. انقر على "تشخيص المشاكل"
4. اختبر الاتصال والكتابة والقراءة

## خطوات التشخيص

### 1. اختبار الاتصال
```dart
// في شاشة التشخيص
await _database.ref('test').set('connection_test');
```

### 2. اختبار الكتابة
```dart
// اختبار كتابة بيانات المستخدم
await _database.ref('users/${user.uid}').set({
  'test': true,
  'timestamp': DateTime.now().millisecondsSinceEpoch,
});
```

### 3. اختبار القراءة
```dart
// اختبار قراءة البيانات
final snapshot = await _database.ref('users/${user.uid}').get();
```

## الأخطاء الشائعة وحلولها

### خطأ: "Permission denied"
**السبب**: قواعد الأمان مقيدة
**الحل**: تحديث قواعد Realtime Database كما هو موضح أعلاه

### خطأ: "Database URL not found"
**السبب**: رابط قاعدة البيانات غير صحيح
**الحل**: التأكد من رابط قاعدة البيانات في `firebase_options.dart`

### خطأ: "User not authenticated"
**السبب**: المستخدم غير مسجل دخول
**الحل**: التأكد من تسجيل الدخول قبل محاولة الكتابة

### خطأ: "Network error"
**السبب**: مشكلة في الاتصال بالإنترنت
**الحل**: التأكد من الاتصال بالإنترنت

## التحقق من نجاح الإعداد

بعد تطبيق الحلول:

1. **إنشاء حساب جديد**:
   - يجب أن يظهر في Authentication
   - يجب أن تظهر البيانات في Realtime Database تحت `users/[uid]`

2. **البيانات المحفوظة**:
   ```json
   {
     "users": {
       "user_uid_here": {
         "uid": "user_uid_here",
         "email": "<EMAIL>",
         "displayName": "اسم المستخدم",
         "phoneNumber": "+1234567890",
         "age": 25,
         "bio": "نبذة شخصية",
         "location": "الموقع",
         "status": "متاح",
         "isOnline": true,
         "createdAt": 1234567890,
         "lastSeen": 1234567890
       }
     }
   }
   ```

## نصائح إضافية

1. **استخدم قواعد الاختبار أولاً** للتأكد من عمل كل شيء
2. **انتقل لقواعد الإنتاج** بعد التأكد من عمل التطبيق
3. **راقب Console** للأخطاء أثناء التطوير
4. **استخدم شاشة التشخيص** لاختبار الوظائف

## الدعم

إذا استمرت المشكلة:
1. تحقق من Console للأخطاء
2. استخدم شاشة التشخيص في التطبيق
3. تأكد من إعدادات Firebase
4. تحقق من اتصال الإنترنت

---

**ملاحظة مهمة**: قواعد الاختبار تسمح لأي مستخدم مسجل دخول بالقراءة والكتابة. استخدمها فقط أثناء التطوير وانتقل لقواعد الإنتاج الآمنة عند النشر.
