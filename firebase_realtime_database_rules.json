{"rules": {"users": {"$uid": {".read": "$uid === auth.uid || root.child('users').child(auth.uid).exists()", ".write": "$uid === auth.uid"}}, "chats": {"$chatId": {".read": "root.child('chats').child($chatId).child('participants').child(auth.uid).exists() || root.child('chats').child($chatId).child('participants').val().contains(auth.uid)", ".write": "root.child('chats').child($chatId).child('participants').child(auth.uid).exists() || root.child('chats').child($chatId).child('participants').val().contains(auth.uid)"}}, "messages": {"$chatId": {".read": "root.child('chats').child($chatId).child('participants').val().contains(auth.uid)", ".write": "root.child('chats').child($chatId).child('participants').val().contains(auth.uid)"}}, ".read": false, ".write": false}}