{"rules": {"users": {"$uid": {".read": "$uid === auth.uid", ".write": "$uid === auth.uid", ".validate": "newData.hasChildren(['uid', 'email', 'displayName', 'createdAt', 'lastSeen', 'isOnline'])"}}, "chats": {"$chatId": {".read": "root.child('chats').child($chatId).child('participants').child(auth.uid).exists()", ".write": "root.child('chats').child($chatId).child('participants').child(auth.uid).exists()", "participants": {"$participantId": {".validate": "$participantId === auth.uid || root.child('chats').child($chatId).child('participants').child(auth.uid).exists()"}}}}, "messages": {"$chatId": {".read": "root.child('chats').child($chatId).child('participants').child(auth.uid).exists()", ".write": "root.child('chats').child($chatId).child('participants').child(auth.uid).exists()", "$messageId": {".validate": "newData.child('senderId').val() === auth.uid"}}}, ".read": false, ".write": false}}