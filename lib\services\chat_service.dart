import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:firebase_storage/firebase_storage.dart';
import 'package:uuid/uuid.dart';
import 'dart:io';
import '../models/message_model.dart';
import '../models/chat_model.dart';
import '../models/user_model.dart';
import 'realtime_database_service.dart';

class ChatService {
  final FirebaseFirestore _firestore = FirebaseFirestore.instance;
  final FirebaseStorage _storage = FirebaseStorage.instance;
  final Uuid _uuid = const Uuid();
  final RealtimeDatabaseService _realtimeDB = RealtimeDatabaseService();

  // Get or create chat between two users
  Future<String> getOrCreateChat(
    String currentUserId,
    String otherUserId,
  ) async {
    try {
      // Use Realtime Database for chat creation
      String chatId = await _realtimeDB.getOrCreateChat(
        currentUserId,
        otherUserId,
      );

      // Also create in Firestore for compatibility
      List<String> participants = [currentUserId, otherUserId];
      participants.sort();
      String firestoreChatId = participants.join('_');

      DocumentSnapshot chatDoc =
          await _firestore.collection('chats').doc(firestoreChatId).get();

      if (!chatDoc.exists) {
        ChatModel newChat = ChatModel(
          id: firestoreChatId,
          participants: participants,
          lastActivity: DateTime.now(),
        );
        await _firestore
            .collection('chats')
            .doc(firestoreChatId)
            .set(newChat.toMap());
      }

      return chatId;
    } catch (e) {
      throw Exception('Error creating chat: $e');
    }
  }

  // Send text message
  Future<void> sendMessage(
    String chatId,
    String senderId,
    String receiverId,
    String content,
  ) async {
    try {
      String messageId = _uuid.v4();

      MessageModel message = MessageModel(
        id: messageId,
        senderId: senderId,
        receiverId: receiverId,
        content: content,
        type: MessageType.text,
        timestamp: DateTime.now(),
        status: MessageStatus.sent,
      );

      // Send message using Realtime Database
      await _realtimeDB.sendMessage(chatId, message);

      // Also save to Firestore for compatibility
      await _firestore
          .collection('chats')
          .doc(chatId)
          .collection('messages')
          .doc(messageId)
          .set(message.toMap());

      await _firestore.collection('chats').doc(chatId).update({
        'lastActivity': DateTime.now().millisecondsSinceEpoch,
        'lastMessage': message.toMap(),
      });
    } catch (e) {
      throw Exception('Error sending message: $e');
    }
  }

  // Send image message
  Future<void> sendImageMessage(
    String chatId,
    String senderId,
    String receiverId,
    File imageFile,
  ) async {
    try {
      String messageId = _uuid.v4();

      // Upload image to Firebase Storage
      String imagePath = 'chat_images/$chatId/$messageId.jpg';
      UploadTask uploadTask = _storage
          .ref()
          .child(imagePath)
          .putFile(imageFile);
      TaskSnapshot snapshot = await uploadTask;
      String imageUrl = await snapshot.ref.getDownloadURL();

      MessageModel message = MessageModel(
        id: messageId,
        senderId: senderId,
        receiverId: receiverId,
        content: 'صورة',
        type: MessageType.image,
        timestamp: DateTime.now(),
        status: MessageStatus.sent,
        imageUrl: imageUrl,
      );

      // Send image message using Realtime Database
      await _realtimeDB.sendMessage(chatId, message);

      // Also save to Firestore for compatibility
      await _firestore
          .collection('chats')
          .doc(chatId)
          .collection('messages')
          .doc(messageId)
          .set(message.toMap());

      await _firestore.collection('chats').doc(chatId).update({
        'lastActivity': DateTime.now().millisecondsSinceEpoch,
        'lastMessage': message.toMap(),
      });
    } catch (e) {
      throw Exception('Error sending image: $e');
    }
  }

  // Get messages stream
  Stream<List<MessageModel>> getMessages(String chatId) {
    // Use Realtime Database for real-time messages
    return _realtimeDB.getMessages(chatId);
  }

  // Get user chats stream
  Stream<List<ChatModel>> getUserChats(String userId) {
    // Use Realtime Database for real-time chat updates
    return _realtimeDB.getUserChats(userId);
  }

  // Mark messages as read
  Future<void> markMessagesAsRead(String chatId, String userId) async {
    try {
      QuerySnapshot unreadMessages =
          await _firestore
              .collection('chats')
              .doc(chatId)
              .collection('messages')
              .where('receiverId', isEqualTo: userId)
              .where('status', isNotEqualTo: 'read')
              .get();

      WriteBatch batch = _firestore.batch();

      for (QueryDocumentSnapshot doc in unreadMessages.docs) {
        batch.update(doc.reference, {'status': 'read'});
      }

      await batch.commit();
    } catch (e) {
      print('Error marking messages as read: $e');
    }
  }

  // Get user data
  Future<UserModel?> getUserData(String userId) async {
    try {
      // Try to get from Realtime Database first
      UserModel? user = await _realtimeDB.getUserData(userId);
      if (user != null) {
        return user;
      }

      // Fallback to Firestore
      DocumentSnapshot doc =
          await _firestore.collection('users').doc(userId).get();
      if (doc.exists) {
        return UserModel.fromMap(doc.data() as Map<String, dynamic>);
      }
      return null;
    } catch (e) {
      print('Error getting user data: $e');
      return null;
    }
  }
}
