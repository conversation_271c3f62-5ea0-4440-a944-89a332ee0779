import 'message_model.dart';
import 'user_model.dart';

class ChatModel {
  final String id;
  final List<String> participants;
  final MessageModel? lastMessage;
  final DateTime lastActivity;
  final int unreadCount;
  final UserModel? otherUser;

  ChatModel({
    required this.id,
    required this.participants,
    this.lastMessage,
    required this.lastActivity,
    this.unreadCount = 0,
    this.otherUser,
  });

  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'participants': participants,
      'lastMessage': lastMessage?.toMap(),
      'lastActivity': lastActivity.millisecondsSinceEpoch,
      'unreadCount': unreadCount,
    };
  }

  factory ChatModel.fromMap(Map<String, dynamic> map) {
    return ChatModel(
      id: map['id'] ?? '',
      participants: List<String>.from(map['participants'] ?? []),
      lastMessage: map['lastMessage'] != null
          ? MessageModel.fromMap(map['lastMessage'])
          : null,
      lastActivity: DateTime.fromMillisecondsSinceEpoch(map['lastActivity'] ?? 0),
      unreadCount: map['unreadCount'] ?? 0,
    );
  }

  ChatModel copyWith({
    String? id,
    List<String>? participants,
    MessageModel? lastMessage,
    DateTime? lastActivity,
    int? unreadCount,
    UserModel? otherUser,
  }) {
    return ChatModel(
      id: id ?? this.id,
      participants: participants ?? this.participants,
      lastMessage: lastMessage ?? this.lastMessage,
      lastActivity: lastActivity ?? this.lastActivity,
      unreadCount: unreadCount ?? this.unreadCount,
      otherUser: otherUser ?? this.otherUser,
    );
  }
}
