// File generated by FlutterFire CLI.
// ignore_for_file: lines_longer_than_80_chars, avoid_classes_with_only_static_members
import 'package:firebase_core/firebase_core.dart' show FirebaseOptions;
import 'package:flutter/foundation.dart'
    show defaultTargetPlatform, kIsWeb, TargetPlatform;

/// Default [FirebaseOptions] for use with your Firebase apps.
///
/// Example:
/// ```dart
/// import 'firebase_options.dart';
/// // ...
/// await Firebase.initializeApp(
///   options: DefaultFirebaseOptions.currentPlatform,
/// );
/// ```
class DefaultFirebaseOptions {
  static FirebaseOptions get currentPlatform {
    if (kIsWeb) {
      return web;
    }
    switch (defaultTargetPlatform) {
      case TargetPlatform.android:
        return android;
      case TargetPlatform.iOS:
        return ios;
      case TargetPlatform.macOS:
        return macos;
      case TargetPlatform.windows:
        throw UnsupportedError(
          'DefaultFirebaseOptions have not been configured for windows - '
          'you can reconfigure this by running the FlutterFire CLI again.',
        );
      case TargetPlatform.linux:
        throw UnsupportedError(
          'DefaultFirebaseOptions have not been configured for linux - '
          'you can reconfigure this by running the FlutterFire CLI again.',
        );
      default:
        throw UnsupportedError(
          'DefaultFirebaseOptions are not supported for this platform.',
        );
    }
  }

  static const FirebaseOptions web = FirebaseOptions(
    apiKey: 'AIzaSyCWqJaxTc5E0je6WaEc9RTlPVeemDDu1kM',
    appId: '1:134457970088:web:50d90a634115461acca19d',
    messagingSenderId: '134457970088',
    projectId: 'chat-app-f404c',
    authDomain: 'chat-app-f404c.firebaseapp.com',
    storageBucket: 'chat-app-f404c.firebasestorage.app',
  );

  static const FirebaseOptions android = FirebaseOptions(
    apiKey: 'AIzaSyCWqJaxTc5E0je6WaEc9RTlPVeemDDu1kM',
    appId: '1:134457970088:android:50d90a634115461acca19d',
    messagingSenderId: '134457970088',
    projectId: 'chat-app-f404c',
    storageBucket: 'chat-app-f404c.firebasestorage.app',
  );

  static const FirebaseOptions ios = FirebaseOptions(
    apiKey: 'AIzaSyCWqJaxTc5E0je6WaEc9RTlPVeemDDu1kM',
    appId: '1:134457970088:ios:50d90a634115461acca19d',
    messagingSenderId: '134457970088',
    projectId: 'chat-app-f404c',
    storageBucket: 'chat-app-f404c.firebasestorage.app',
    iosBundleId: 'com.spider_chat5.spider_chat',
  );

  static const FirebaseOptions macos = FirebaseOptions(
    apiKey: 'AIzaSyCWqJaxTc5E0je6WaEc9RTlPVeemDDu1kM',
    appId: '1:134457970088:macos:50d90a634115461acca19d',
    messagingSenderId: '134457970088',
    projectId: 'chat-app-f404c',
    storageBucket: 'chat-app-f404c.firebasestorage.app',
    iosBundleId: 'com.spider_chat5.spider_chat',
  );
}
