# Spider Chat - تطبيق دردشة مثل الواتساب

تطبيق دردشة حديث مبني بـ Flutter مع Firebase، يحتوي على جميع الميزات الأساسية للمراسلة الفورية.

## الميزات

- ✅ تسجيل الدخول وإنشاء الحساب بالبريد الإلكتروني
- ✅ واجهة مستخدم داكنة (أبيض وأسود)
- ✅ إرسال واستقبال الرسائل النصية في الوقت الفعلي
- ✅ إرسال الصور
- ✅ حالة الرسائل (مرسلة، مستلمة، مقروءة)
- ✅ حالة الاتصال (متصل/غير متصل)
- ✅ البحث عن المستخدمين
- ✅ الملف الشخصي

## إعداد Firebase

لتشغيل التطبيق، تحتاج إلى إعداد مشروع Firebase:

### 1. إنشاء مشروع Firebase
1. اذهب إلى [Firebase Console](https://console.firebase.google.com/)
2. انقر على "إنشاء مشروع"
3. اتبع الخطوات لإنشاء المشروع

### 2. تفعيل الخدمات المطلوبة
في مشروع Firebase الخاص بك:

#### Authentication
1. اذهب إلى Authentication > Sign-in method
2. فعّل "Email/Password"

#### Firestore Database
1. اذهب إلى Firestore Database
2. انقر على "إنشاء قاعدة بيانات"
3. اختر "Start in test mode" للبداية

#### Storage
1. اذهب إلى Storage
2. انقر على "البدء"
3. اختر "Start in test mode"

### 3. إضافة التطبيق إلى Firebase
1. في Firebase Console، انقر على أيقونة Android/iOS
2. اتبع الخطوات لإضافة التطبيق
3. حمّل ملف `google-services.json` (Android) أو `GoogleService-Info.plist` (iOS)

### 4. تحديث إعدادات Firebase
في ملف `lib/firebase_options.dart`، استبدل القيم التالية بقيم مشروعك:
- `your-project-id`
- `your-api-key`
- `your-app-id`
- `your-sender-id`

## تشغيل التطبيق

```bash
# تثبيت التبعيات
flutter pub get

# تشغيل التطبيق
flutter run
```

## هيكل المشروع

```
lib/
├── models/          # نماذج البيانات
├── providers/       # مزودي الحالة
├── screens/         # شاشات التطبيق
├── services/        # خدمات Firebase
├── utils/           # أدوات مساعدة
├── widgets/         # مكونات قابلة للإعادة
└── main.dart        # نقطة البداية
```

## التقنيات المستخدمة

- **Flutter** - إطار العمل الأساسي
- **Firebase Auth** - المصادقة
- **Cloud Firestore** - قاعدة البيانات
- **Firebase Storage** - تخزين الصور
- **Provider** - إدارة الحالة
- **Material Design** - تصميم الواجهة

## المساهمة

مرحب بالمساهمات! يرجى فتح issue أو pull request.

## الترخيص

هذا المشروع مرخص تحت رخصة MIT.
