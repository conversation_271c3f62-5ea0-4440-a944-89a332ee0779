# Spider Chat - تطبيق دردشة متقدم

تطبيق دردشة حديث ومتطور مبني بـ Flutter مع Firebase Realtime Database، يحتوي على جميع الميزات المتقدمة للمراسلة الفورية.

## الميزات الجديدة والمحسنة

### 🔥 الميزات الأساسية
- ✅ تسجيل الدخول وإنشاء الحساب بالبريد الإلكتروني
- ✅ واجهة مستخدم داكنة عصرية (أبيض وأسود)
- ✅ إرسال واستقبال الرسائل النصية في الوقت الفعلي
- ✅ إرسال الصور مع معاينة
- ✅ حالة الرسائل (مرسلة، مستلمة، مقروءة)
- ✅ حالة الاتصال التلقائية (متصل/غير متصل)

### 🚀 الميزات المتقدمة الجديدة
- ✅ **Firebase Realtime Database** - للمراسلة الفورية السريعة
- ✅ **معلومات المستخدم الإضافية** - رقم الهاتف، العمر، النبذة، الموقع، الحالة
- ✅ **شاشة تحديث الملف الشخصي** - تحديث جميع المعلومات الشخصية
- ✅ **البحث المحسن** - البحث بالاسم والبريد الإلكتروني مع واجهة محسنة
- ✅ **إحصائيات المستخدم** - عرض إحصائيات الاستخدام والنشاط
- ✅ **إعدادات الإشعارات** - تخصيص كامل للإشعارات والأصوات
- ✅ **حالة الاتصال التلقائية** - تحديث تلقائي عند الدخول والخروج

## إعداد Firebase

لتشغيل التطبيق، تحتاج إلى إعداد مشروع Firebase:

### 1. إنشاء مشروع Firebase
1. اذهب إلى [Firebase Console](https://console.firebase.google.com/)
2. انقر على "إنشاء مشروع"
3. اتبع الخطوات لإنشاء المشروع

### 2. تفعيل الخدمات المطلوبة
في مشروع Firebase الخاص بك:

#### Authentication
1. اذهب إلى Authentication > Sign-in method
2. فعّل "Email/Password"

#### Realtime Database (الجديد - مطلوب)
1. اذهب إلى Realtime Database
2. انقر على "إنشاء قاعدة بيانات"
3. اختر "Start in test mode" للبداية
4. استخدم الرابط: `https://chat-app-f404c-default-rtdb.firebaseio.com/`

#### Firestore Database (للتوافق)
1. اذهب إلى Firestore Database
2. انقر على "إنشاء قاعدة بيانات"
3. اختر "Start in test mode" للبداية

#### Storage
1. اذهب إلى Storage
2. انقر على "البدء"
3. اختر "Start in test mode"

### 3. إضافة التطبيق إلى Firebase
1. في Firebase Console، انقر على أيقونة Android/iOS
2. اتبع الخطوات لإضافة التطبيق
3. حمّل ملف `google-services.json` (Android) أو `GoogleService-Info.plist` (iOS)

### 4. تحديث إعدادات Firebase
في ملف `lib/firebase_options.dart`، استبدل القيم التالية بقيم مشروعك:
- `your-project-id`
- `your-api-key`
- `your-app-id`
- `your-sender-id`

## تشغيل التطبيق

```bash
# تثبيت التبعيات
flutter pub get

# تشغيل التطبيق
flutter run
```

## هيكل المشروع

```
lib/
├── models/          # نماذج البيانات
├── providers/       # مزودي الحالة
├── screens/         # شاشات التطبيق
├── services/        # خدمات Firebase
├── utils/           # أدوات مساعدة
├── widgets/         # مكونات قابلة للإعادة
└── main.dart        # نقطة البداية
```

## التقنيات المستخدمة

- **Flutter** - إطار العمل الأساسي
- **Firebase Auth** - المصادقة والتحقق
- **Firebase Realtime Database** - قاعدة البيانات الفورية (الجديد)
- **Cloud Firestore** - قاعدة البيانات الاحتياطية
- **Firebase Storage** - تخزين الصور والملفات
- **Provider** - إدارة الحالة
- **SharedPreferences** - حفظ الإعدادات المحلية
- **Material Design** - تصميم الواجهة العصري

## الميزات الجديدة المضافة

### 🔧 تحسينات البنية التحتية
- **نظام قاعدة البيانات المزدوج**: استخدام Realtime Database للمراسلة الفورية مع Firestore كنسخة احتياطية
- **إدارة حالة الاتصال التلقائية**: تحديث تلقائي لحالة المستخدم عند الدخول والخروج
- **نظام البحث المحسن**: بحث سريع ودقيق بالاسم والبريد الإلكتروني

### 👤 تحسينات الملف الشخصي
- **معلومات إضافية**: رقم الهاتف، العمر، النبذة الشخصية، الموقع، الحالة
- **شاشة تحديث متقدمة**: واجهة سهلة لتحديث جميع المعلومات الشخصية
- **إحصائيات مفصلة**: عرض إحصائيات الاستخدام والنشاط

### ⚙️ إعدادات متقدمة
- **إعدادات الإشعارات**: تحكم كامل في الإشعارات والأصوات والاهتزاز
- **حفظ الإعدادات محلياً**: استخدام SharedPreferences لحفظ تفضيلات المستخدم

### 🎨 تحسينات الواجهة
- **تصميم عصري**: واجهات محسنة مع تدرجات لونية وتأثيرات بصرية
- **تجربة مستخدم محسنة**: تنقل سلس وواجهات تفاعلية
- **رسائل تأكيد وخطأ**: تغذية راجعة واضحة للمستخدم

## كيفية الاستخدام

### 1. إنشاء حساب جديد
- افتح التطبيق واضغط على "إنشاء حساب جديد"
- أدخل الاسم والبريد الإلكتروني وكلمة المرور
- اختيارياً: أضف رقم الهاتف والعمر
- اضغط "إنشاء الحساب"

### 2. تحديث الملف الشخصي
- اذهب إلى الملف الشخصي
- اضغط على "تحديث الملف الشخصي"
- أضف النبذة الشخصية والموقع والحالة
- احفظ التغييرات

### 3. البحث والدردشة
- اضغط على أيقونة البحث في الشاشة الرئيسية
- ابحث عن المستخدمين بالاسم أو البريد الإلكتروني
- اضغط "دردشة" لبدء محادثة جديدة

### 4. عرض الإحصائيات
- اذهب إلى الملف الشخصي
- اضغط على "إحصائياتي"
- اعرض إحصائيات الاستخدام والنشاط

### 5. إعدادات الإشعارات
- اذهب إلى الملف الشخصي
- اضغط على "الإشعارات"
- خصص إعدادات الإشعارات والأصوات

## المساهمة

مرحب بالمساهمات! يرجى فتح issue أو pull request.

## الترخيص

هذا المشروع مرخص تحت رخصة MIT.
