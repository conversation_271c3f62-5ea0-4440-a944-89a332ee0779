import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../../providers/auth_provider.dart';
import '../auth/login_screen.dart';
import 'edit_profile_screen.dart';

class ProfileScreen extends StatelessWidget {
  const ProfileScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('الملف الشخصي'),
        leading: IconButton(
          icon: const Icon(Icons.arrow_back),
          onPressed: () => Navigator.of(context).pop(),
        ),
      ),
      body: Consumer<AuthProvider>(
        builder: (context, authProvider, child) {
          if (authProvider.userModel == null) {
            return const Center(child: CircularProgressIndicator());
          }

          return Padding(
            padding: const EdgeInsets.all(24.0),
            child: Column(
              children: [
                const SizedBox(height: 32),

                // Profile Picture
                CircleAvatar(
                  radius: 60,
                  backgroundColor: Colors.white,
                  child:
                      authProvider.userModel!.photoURL != null
                          ? ClipOval(
                            child: Image.network(
                              authProvider.userModel!.photoURL!,
                              width: 120,
                              height: 120,
                              fit: BoxFit.cover,
                              errorBuilder: (context, error, stackTrace) {
                                return Text(
                                  authProvider.userModel!.displayName.isNotEmpty
                                      ? authProvider.userModel!.displayName[0]
                                          .toUpperCase()
                                      : 'U',
                                  style: const TextStyle(
                                    color: Colors.black,
                                    fontSize: 48,
                                    fontWeight: FontWeight.bold,
                                  ),
                                );
                              },
                            ),
                          )
                          : Text(
                            authProvider.userModel!.displayName.isNotEmpty
                                ? authProvider.userModel!.displayName[0]
                                    .toUpperCase()
                                : 'U',
                            style: const TextStyle(
                              color: Colors.black,
                              fontSize: 48,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                ),
                const SizedBox(height: 24),

                // User Info
                Card(
                  child: Padding(
                    padding: const EdgeInsets.all(16),
                    child: Column(
                      children: [
                        ListTile(
                          leading: const Icon(Icons.person),
                          title: const Text('الاسم'),
                          subtitle: Text(authProvider.userModel!.displayName),
                        ),
                        const Divider(),
                        ListTile(
                          leading: const Icon(Icons.email),
                          title: const Text('البريد الإلكتروني'),
                          subtitle: Text(authProvider.userModel!.email),
                        ),
                        const Divider(),
                        if (authProvider.userModel!.phoneNumber != null) ...[
                          ListTile(
                            leading: const Icon(Icons.phone),
                            title: const Text('رقم الهاتف'),
                            subtitle: Text(
                              authProvider.userModel!.phoneNumber!,
                            ),
                          ),
                          const Divider(),
                        ],
                        if (authProvider.userModel!.age != null) ...[
                          ListTile(
                            leading: const Icon(Icons.cake),
                            title: const Text('العمر'),
                            subtitle: Text(
                              '${authProvider.userModel!.age} سنة',
                            ),
                          ),
                          const Divider(),
                        ],
                        if (authProvider.userModel!.bio != null) ...[
                          ListTile(
                            leading: const Icon(Icons.info),
                            title: const Text('نبذة عني'),
                            subtitle: Text(authProvider.userModel!.bio!),
                          ),
                          const Divider(),
                        ],
                        if (authProvider.userModel!.location != null) ...[
                          ListTile(
                            leading: const Icon(Icons.location_on),
                            title: const Text('الموقع'),
                            subtitle: Text(authProvider.userModel!.location!),
                          ),
                          const Divider(),
                        ],
                        if (authProvider.userModel!.status != null) ...[
                          ListTile(
                            leading: const Icon(Icons.mood),
                            title: const Text('الحالة'),
                            subtitle: Text(authProvider.userModel!.status!),
                          ),
                          const Divider(),
                        ],
                        ListTile(
                          leading: Icon(
                            authProvider.userModel!.isOnline
                                ? Icons.circle
                                : Icons.circle_outlined,
                            color:
                                authProvider.userModel!.isOnline
                                    ? Colors.green
                                    : Colors.grey,
                          ),
                          title: const Text('حالة الاتصال'),
                          subtitle: Text(
                            authProvider.userModel!.isOnline
                                ? 'متصل الآن'
                                : 'غير متصل',
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
                const SizedBox(height: 16),

                // Edit Profile Button
                SizedBox(
                  width: double.infinity,
                  child: ElevatedButton.icon(
                    onPressed: () {
                      Navigator.of(context).push(
                        MaterialPageRoute(
                          builder: (context) => const EditProfileScreen(),
                        ),
                      );
                    },
                    icon: const Icon(Icons.edit),
                    label: const Text('تحديث الملف الشخصي'),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Colors.blue,
                      foregroundColor: Colors.white,
                    ),
                  ),
                ),
                const SizedBox(height: 32),

                // Settings Section
                Card(
                  child: Column(
                    children: [
                      ListTile(
                        leading: const Icon(Icons.notifications),
                        title: const Text('الإشعارات'),
                        trailing: Switch(
                          value: true,
                          onChanged: (value) {
                            // TODO: Implement notification settings
                          },
                        ),
                      ),
                      const Divider(),
                      ListTile(
                        leading: const Icon(Icons.dark_mode),
                        title: const Text('الوضع الداكن'),
                        trailing: Switch(
                          value: true,
                          onChanged: (value) {
                            // TODO: Implement theme switching
                          },
                        ),
                      ),
                      const Divider(),
                      ListTile(
                        leading: const Icon(Icons.language),
                        title: const Text('اللغة'),
                        subtitle: const Text('العربية'),
                        trailing: const Icon(Icons.arrow_forward_ios),
                        onTap: () {
                          // TODO: Implement language selection
                        },
                      ),
                    ],
                  ),
                ),
                const Spacer(),

                // Logout Button
                SizedBox(
                  width: double.infinity,
                  child: ElevatedButton.icon(
                    onPressed: () async {
                      bool? confirm = await showDialog<bool>(
                        context: context,
                        builder:
                            (context) => AlertDialog(
                              title: const Text('تسجيل الخروج'),
                              content: const Text(
                                'هل أنت متأكد من تسجيل الخروج؟',
                              ),
                              actions: [
                                TextButton(
                                  onPressed:
                                      () => Navigator.of(context).pop(false),
                                  child: const Text('إلغاء'),
                                ),
                                TextButton(
                                  onPressed:
                                      () => Navigator.of(context).pop(true),
                                  child: const Text('تسجيل الخروج'),
                                ),
                              ],
                            ),
                      );

                      if (confirm == true) {
                        await authProvider.signOut();
                        if (context.mounted) {
                          Navigator.of(context).pushAndRemoveUntil(
                            MaterialPageRoute(
                              builder: (context) => const LoginScreen(),
                            ),
                            (route) => false,
                          );
                        }
                      }
                    },
                    icon: const Icon(Icons.logout),
                    label: const Text('تسجيل الخروج'),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Colors.red,
                      foregroundColor: Colors.white,
                    ),
                  ),
                ),
                const SizedBox(height: 16),
              ],
            ),
          );
        },
      ),
    );
  }
}
