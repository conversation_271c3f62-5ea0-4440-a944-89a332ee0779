# دليل البدء السريع - Spider Chat

## 🚀 تشغيل التطبيق بسرعة

### الطريقة الأولى: استخدام ملف التشغيل
```bash
# في Windows
run_app.bat

# في Mac/Linux
chmod +x run_app.sh
./run_app.sh
```

### الطريقة الثانية: الأوامر اليدوية
```bash
# تثبيت التبعيات
flutter pub get

# تشغيل التطبيق
flutter run
```

## 📱 اختبار التطبيق

### 1. إنشاء حساب جديد
- افتح التطبيق
- انقر على "إنشاء حساب جديد"
- املأ البيانات المطلوبة
- انقر على "إنشاء الحساب"

### 2. تسجيل الدخول
- استخدم البريد الإلكتروني وكلمة المرور
- انقر على "تسجيل الدخول"

### 3. بدء محادثة
- انقر على زر "+" في الشاشة الرئيسية
- ابحث عن مستخدم بالبريد الإلكتروني
- انقر على المستخدم لبدء المحادثة

### 4. إرسال الرسائل
- اكتب رسالة في حقل النص
- انقر على زر الإرسال
- أو انقر على أيقونة الصورة لإرسال صورة

## 🔧 إعداد Firebase (مطلوب للإنتاج)

### 1. إنشاء مشروع Firebase
1. اذهب إلى [Firebase Console](https://console.firebase.google.com/)
2. انقر على "إنشاء مشروع"
3. اتبع الخطوات

### 2. تفعيل الخدمات
- **Authentication**: فعّل Email/Password
- **Firestore Database**: ابدأ في وضع الاختبار
- **Storage**: ابدأ في وضع الاختبار

### 3. إضافة التطبيق
1. أضف تطبيق Android/iOS
2. حمّل ملف التكوين
3. ضعه في المكان المناسب

### 4. تحديث الإعدادات
- حدّث `lib/firebase_options.dart` بقيم مشروعك
- استبدل `google-services.json` (Android)
- أضف `GoogleService-Info.plist` (iOS)

## 🎨 الميزات المتاحة

✅ **تسجيل الدخول والتسجيل**
- إنشاء حساب بالبريد الإلكتروني
- تسجيل الدخول
- تسجيل الخروج

✅ **المحادثات**
- قائمة المحادثات
- إرسال الرسائل النصية
- إرسال الصور
- حالة الرسائل

✅ **البحث**
- البحث عن المستخدمين
- بدء محادثات جديدة

✅ **الملف الشخصي**
- عرض معلومات المستخدم
- الإعدادات الأساسية

✅ **التصميم**
- واجهة داكنة (أبيض وأسود)
- تصميم عصري ومتجاوب

## 🐛 حل المشاكل الشائعة

### مشكلة: التطبيق لا يتصل بـ Firebase
**الحل**: تأكد من:
- إعداد Firebase بشكل صحيح
- تحديث ملف firebase_options.dart
- تفعيل الخدمات المطلوبة

### مشكلة: لا يمكن إرسال الصور
**الحل**: تأكد من:
- تفعيل Firebase Storage
- إعطاء الصلاحيات المناسبة
- التحقق من اتصال الإنترنت

### مشكلة: الرسائل لا تظهر في الوقت الفعلي
**الحل**: تأكد من:
- إعداد Firestore بشكل صحيح
- قواعد الأمان مناسبة
- اتصال الإنترنت مستقر

## 📞 الدعم

إذا واجهت أي مشاكل:
1. تحقق من ملف README.md للتفاصيل الكاملة
2. راجع إعدادات Firebase
3. تأكد من تثبيت جميع التبعيات

---

**تم إنشاء التطبيق بواسطة Flutter + Firebase**
