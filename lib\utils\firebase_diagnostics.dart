import 'package:firebase_database/firebase_database.dart';
import 'package:firebase_auth/firebase_auth.dart';
import '../models/user_model.dart';

class FirebaseDiagnostics {
  static final FirebaseDatabase _database = FirebaseDatabase.instance;
  static final FirebaseAuth _auth = FirebaseAuth.instance;

  /// اختبار شامل لاتصال Firebase
  static Future<Map<String, dynamic>> runDiagnostics() async {
    final results = <String, dynamic>{};
    
    try {
      // 1. اختبار إعدادات Firebase
      results['firebase_config'] = await _testFirebaseConfig();
      
      // 2. اختبار Authentication
      results['auth_test'] = await _testAuthentication();
      
      // 3. اختبار Realtime Database
      results['database_test'] = await _testRealtimeDatabase();
      
      // 4. اختبار قواعد الأمان
      results['security_rules'] = await _testSecurityRules();
      
      results['overall_status'] = 'success';
      results['timestamp'] = DateTime.now().toIso8601String();
      
    } catch (e) {
      results['overall_status'] = 'failed';
      results['error'] = e.toString();
      results['timestamp'] = DateTime.now().toIso8601String();
    }
    
    return results;
  }

  /// اختبار إعدادات Firebase
  static Future<Map<String, dynamic>> _testFirebaseConfig() async {
    try {
      final app = _database.app;
      return {
        'status': 'success',
        'project_id': app.options.projectId,
        'database_url': app.options.databaseURL,
        'app_id': app.options.appId,
        'api_key_exists': app.options.apiKey.isNotEmpty,
      };
    } catch (e) {
      return {
        'status': 'failed',
        'error': e.toString(),
      };
    }
  }

  /// اختبار Authentication
  static Future<Map<String, dynamic>> _testAuthentication() async {
    try {
      final currentUser = _auth.currentUser;
      return {
        'status': 'success',
        'current_user_exists': currentUser != null,
        'user_uid': currentUser?.uid,
        'user_email': currentUser?.email,
        'user_display_name': currentUser?.displayName,
      };
    } catch (e) {
      return {
        'status': 'failed',
        'error': e.toString(),
      };
    }
  }

  /// اختبار Realtime Database
  static Future<Map<String, dynamic>> _testRealtimeDatabase() async {
    try {
      // اختبار الاتصال
      await _database.goOnline();
      
      // اختبار القراءة
      final testRef = _database.ref('test_connection');
      final testData = {'timestamp': DateTime.now().millisecondsSinceEpoch};
      
      // اختبار الكتابة
      await testRef.set(testData);
      
      // اختبار القراءة
      final snapshot = await testRef.get();
      final readSuccess = snapshot.exists;
      
      // تنظيف البيانات التجريبية
      await testRef.remove();
      
      return {
        'status': 'success',
        'connection': 'online',
        'write_test': 'success',
        'read_test': readSuccess ? 'success' : 'failed',
        'database_url': _database.app.options.databaseURL,
      };
    } catch (e) {
      return {
        'status': 'failed',
        'error': e.toString(),
        'error_type': e.runtimeType.toString(),
      };
    }
  }

  /// اختبار قواعد الأمان
  static Future<Map<String, dynamic>> _testSecurityRules() async {
    try {
      final currentUser = _auth.currentUser;
      
      if (currentUser == null) {
        return {
          'status': 'skipped',
          'reason': 'no_authenticated_user',
        };
      }

      // اختبار الوصول إلى users node
      final usersRef = _database.ref('users');
      
      try {
        // محاولة قراءة البيانات
        final snapshot = await usersRef.limitToFirst(1).get();
        
        // محاولة كتابة بيانات تجريبية
        final testUserRef = usersRef.child('test_${currentUser.uid}');
        await testUserRef.set({'test': true});
        await testUserRef.remove();
        
        return {
          'status': 'success',
          'read_access': 'allowed',
          'write_access': 'allowed',
          'user_authenticated': true,
        };
      } catch (e) {
        return {
          'status': 'failed',
          'error': e.toString(),
          'user_authenticated': true,
          'possible_cause': 'security_rules_too_restrictive',
        };
      }
    } catch (e) {
      return {
        'status': 'failed',
        'error': e.toString(),
      };
    }
  }

  /// اختبار إنشاء مستخدم تجريبي
  static Future<Map<String, dynamic>> testUserCreation() async {
    try {
      final currentUser = _auth.currentUser;
      
      if (currentUser == null) {
        return {
          'status': 'failed',
          'error': 'no_authenticated_user',
        };
      }

      // إنشاء بيانات مستخدم تجريبية
      final testUser = UserModel(
        uid: currentUser.uid,
        email: currentUser.email ?? '<EMAIL>',
        displayName: currentUser.displayName ?? 'Test User',
        createdAt: DateTime.now(),
        lastSeen: DateTime.now(),
        isOnline: true,
        phoneNumber: '1234567890',
        age: 25,
        status: 'متاح',
      );

      // محاولة حفظ البيانات
      final userRef = _database.ref('users').child(currentUser.uid);
      await userRef.set(testUser.toMap());
      
      // التحقق من الحفظ
      final snapshot = await userRef.get();
      
      if (snapshot.exists) {
        final savedData = Map<String, dynamic>.from(snapshot.value as Map);
        return {
          'status': 'success',
          'data_saved': true,
          'saved_data': savedData,
        };
      } else {
        return {
          'status': 'failed',
          'data_saved': false,
          'error': 'data_not_found_after_save',
        };
      }
    } catch (e) {
      return {
        'status': 'failed',
        'error': e.toString(),
        'error_type': e.runtimeType.toString(),
      };
    }
  }

  /// طباعة تقرير التشخيص
  static void printDiagnosticsReport(Map<String, dynamic> results) {
    print('\n' + '=' * 50);
    print('🔍 Firebase Diagnostics Report');
    print('=' * 50);
    
    results.forEach((key, value) {
      print('\n📊 $key:');
      if (value is Map) {
        value.forEach((subKey, subValue) {
          print('   $subKey: $subValue');
        });
      } else {
        print('   $value');
      }
    });
    
    print('\n' + '=' * 50);
  }
}
