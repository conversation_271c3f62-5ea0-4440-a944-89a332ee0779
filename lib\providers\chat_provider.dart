import 'package:flutter/material.dart';
import 'dart:io';
import '../services/chat_service.dart';
import '../models/message_model.dart';
import '../models/chat_model.dart';
import '../models/user_model.dart';

class ChatProvider with ChangeNotifier {
  final ChatService _chatService = ChatService();
  
  List<ChatModel> _chats = [];
  List<MessageModel> _messages = [];
  bool _isLoading = false;
  String? _errorMessage;
  String? _currentChatId;

  List<ChatModel> get chats => _chats;
  List<MessageModel> get messages => _messages;
  bool get isLoading => _isLoading;
  String? get errorMessage => _errorMessage;
  String? get currentChatId => _currentChatId;

  // Load user chats
  void loadUserChats(String userId) {
    _chatService.getUserChats(userId).listen((chats) async {
      _chats = [];
      
      for (ChatModel chat in chats) {
        // Get other user data
        String otherUserId = chat.participants.firstWhere((id) => id != userId);
        UserModel? otherUser = await _chatService.getUserData(otherUserId);
        
        ChatModel updatedChat = chat.copyWith(otherUser: otherUser);
        _chats.add(updatedChat);
      }
      
      notifyListeners();
    });
  }

  // Load messages for a chat
  void loadMessages(String chatId) {
    _currentChatId = chatId;
    _chatService.getMessages(chatId).listen((messages) {
      _messages = messages;
      notifyListeners();
    });
  }

  // Start or get chat with user
  Future<String?> startChatWithUser(String currentUserId, String otherUserId) async {
    try {
      _setLoading(true);
      String chatId = await _chatService.getOrCreateChat(currentUserId, otherUserId);
      _setLoading(false);
      return chatId;
    } catch (e) {
      _setError(e.toString());
      _setLoading(false);
      return null;
    }
  }

  // Send text message
  Future<void> sendMessage(String chatId, String senderId, String receiverId, String content) async {
    try {
      await _chatService.sendMessage(chatId, senderId, receiverId, content);
    } catch (e) {
      _setError(e.toString());
    }
  }

  // Send image message
  Future<void> sendImageMessage(String chatId, String senderId, String receiverId, File imageFile) async {
    try {
      _setLoading(true);
      await _chatService.sendImageMessage(chatId, senderId, receiverId, imageFile);
      _setLoading(false);
    } catch (e) {
      _setError(e.toString());
      _setLoading(false);
    }
  }

  // Mark messages as read
  Future<void> markMessagesAsRead(String chatId, String userId) async {
    try {
      await _chatService.markMessagesAsRead(chatId, userId);
    } catch (e) {
      _setError(e.toString());
    }
  }

  // Get unread count for a chat
  int getUnreadCount(String chatId, String userId) {
    return _messages.where((message) => 
      message.receiverId == userId && 
      message.status != MessageStatus.read
    ).length;
  }

  void _setLoading(bool loading) {
    _isLoading = loading;
    notifyListeners();
  }

  void _setError(String error) {
    _errorMessage = error;
    notifyListeners();
  }

  void clearError() {
    _errorMessage = null;
    notifyListeners();
  }

  void clearMessages() {
    _messages = [];
    _currentChatId = null;
    notifyListeners();
  }
}
