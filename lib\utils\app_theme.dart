import 'package:flutter/material.dart';

class AppTheme {
  static const Color primaryColor = Color(0xFF000000); // أسود
  static const Color secondaryColor = Color(0xFF1A1A1A); // رمادي داكن
  static const Color backgroundColor = Color(0xFF000000); // أسود
  static const Color surfaceColor = Color(0xFF1A1A1A); // رمادي داكن
  static const Color onPrimaryColor = Color(0xFFFFFFFF); // أبيض
  static const Color onSecondaryColor = Color(0xFFFFFFFF); // أبيض
  static const Color onBackgroundColor = Color(0xFFFFFFFF); // أبيض
  static const Color onSurfaceColor = Color(0xFFFFFFFF); // أبيض
  static const Color accentColor = Color(0xFF333333); // رمادي متوسط
  static const Color buttonColor = Color(0xFFE0E0E0); // رمادي فاتح للأزرار
  static const Color errorColor = Color(0xFFFF4444); // أحمر للأخطاء
  static const Color successColor = Color(0xFF00AA00); // أخضر للنجاح

  static ThemeData get darkTheme {
    return ThemeData(
      useMaterial3: true,
      brightness: Brightness.dark,
      primaryColor: primaryColor,
      scaffoldBackgroundColor: backgroundColor,

      colorScheme: const ColorScheme.dark(
        primary: primaryColor,
        secondary: secondaryColor,
        surface: surfaceColor,
        onPrimary: onPrimaryColor,
        onSecondary: onSecondaryColor,
        onSurface: onSurfaceColor,
        error: errorColor,
      ),

      appBarTheme: const AppBarTheme(
        backgroundColor: primaryColor,
        foregroundColor: onPrimaryColor,
        elevation: 0,
        centerTitle: true,
        titleTextStyle: TextStyle(
          color: onPrimaryColor,
          fontSize: 20,
          fontWeight: FontWeight.bold,
        ),
      ),

      elevatedButtonTheme: ElevatedButtonThemeData(
        style: ElevatedButton.styleFrom(
          backgroundColor: buttonColor, // رمادي فاتح
          foregroundColor: primaryColor, // أسود
          elevation: 0,
          padding: const EdgeInsets.symmetric(horizontal: 32, vertical: 16),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(8),
            side: const BorderSide(color: buttonColor, width: 1),
          ),
        ),
      ),

      textButtonTheme: TextButtonThemeData(
        style: TextButton.styleFrom(
          foregroundColor: onPrimaryColor,
          padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
        ),
      ),

      inputDecorationTheme: InputDecorationTheme(
        filled: true,
        fillColor: surfaceColor,
        border: OutlineInputBorder(
          borderRadius: BorderRadius.circular(8),
          borderSide: const BorderSide(color: accentColor),
        ),
        enabledBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(8),
          borderSide: const BorderSide(color: accentColor),
        ),
        focusedBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(8),
          borderSide: const BorderSide(color: onPrimaryColor, width: 2),
        ),
        errorBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(8),
          borderSide: const BorderSide(color: errorColor),
        ),
        labelStyle: const TextStyle(color: onSurfaceColor),
        hintStyle: TextStyle(color: onSurfaceColor.withValues(alpha: 0.6)),
      ),

      cardTheme: CardTheme(
        color: surfaceColor,
        elevation: 2,
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      ),

      listTileTheme: const ListTileThemeData(
        textColor: onSurfaceColor,
        iconColor: onSurfaceColor,
      ),

      iconTheme: const IconThemeData(color: onSurfaceColor),

      textTheme: const TextTheme(
        displayLarge: TextStyle(
          color: onBackgroundColor,
          fontWeight: FontWeight.bold,
        ),
        displayMedium: TextStyle(
          color: onBackgroundColor,
          fontWeight: FontWeight.bold,
        ),
        displaySmall: TextStyle(
          color: onBackgroundColor,
          fontWeight: FontWeight.bold,
        ),
        headlineLarge: TextStyle(
          color: onBackgroundColor,
          fontWeight: FontWeight.bold,
        ),
        headlineMedium: TextStyle(
          color: onBackgroundColor,
          fontWeight: FontWeight.bold,
        ),
        headlineSmall: TextStyle(
          color: onBackgroundColor,
          fontWeight: FontWeight.bold,
        ),
        titleLarge: TextStyle(
          color: onBackgroundColor,
          fontWeight: FontWeight.w600,
        ),
        titleMedium: TextStyle(
          color: onBackgroundColor,
          fontWeight: FontWeight.w600,
        ),
        titleSmall: TextStyle(
          color: onBackgroundColor,
          fontWeight: FontWeight.w600,
        ),
        bodyLarge: TextStyle(color: onBackgroundColor),
        bodyMedium: TextStyle(color: onBackgroundColor),
        bodySmall: TextStyle(color: onBackgroundColor),
        labelLarge: TextStyle(color: onBackgroundColor),
        labelMedium: TextStyle(color: onBackgroundColor),
        labelSmall: TextStyle(color: onBackgroundColor),
      ),

      floatingActionButtonTheme: const FloatingActionButtonThemeData(
        backgroundColor: primaryColor,
        foregroundColor: onPrimaryColor,
      ),

      bottomNavigationBarTheme: const BottomNavigationBarThemeData(
        backgroundColor: surfaceColor,
        selectedItemColor: onPrimaryColor,
        unselectedItemColor: accentColor,
      ),
    );
  }
}
