import 'package:flutter/material.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:firebase_database/firebase_database.dart';
import '../../services/realtime_database_service.dart';
import '../../models/user_model.dart';

class DebugScreen extends StatefulWidget {
  const DebugScreen({super.key});

  @override
  State<DebugScreen> createState() => _DebugScreenState();
}

class _DebugScreenState extends State<DebugScreen> {
  final RealtimeDatabaseService _realtimeDB = RealtimeDatabaseService();
  final FirebaseDatabase _database = FirebaseDatabase.instance;
  String _debugInfo = '';
  bool _isLoading = false;

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.black,
      appBar: AppBar(
        title: const Text('تشخيص المشاكل'),
        backgroundColor: Colors.black,
        foregroundColor: Colors.white,
      ),
      body: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          children: [
            // Test Buttons
            Wrap(
              spacing: 8,
              runSpacing: 8,
              children: [
                ElevatedButton(
                  onPressed: _testDatabaseConnection,
                  child: const Text('اختبار الاتصال'),
                ),
                ElevatedButton(
                  onPressed: _testWritePermission,
                  child: const Text('اختبار الكتابة'),
                ),
                ElevatedButton(
                  onPressed: _testReadPermission,
                  child: const Text('اختبار القراءة'),
                ),
                ElevatedButton(
                  onPressed: _testUserCreation,
                  child: const Text('اختبار إنشاء مستخدم'),
                ),
                ElevatedButton(
                  onPressed: _checkCurrentUser,
                  child: const Text('فحص المستخدم الحالي'),
                ),
                ElevatedButton(
                  onPressed: _clearDebugInfo,
                  child: const Text('مسح'),
                ),
              ],
            ),
            const SizedBox(height: 20),
            
            // Loading Indicator
            if (_isLoading)
              const CircularProgressIndicator(color: Colors.white),
            
            const SizedBox(height: 20),
            
            // Debug Info
            Expanded(
              child: Container(
                width: double.infinity,
                padding: const EdgeInsets.all(16),
                decoration: BoxDecoration(
                  color: Colors.grey[900],
                  borderRadius: BorderRadius.circular(8),
                ),
                child: SingleChildScrollView(
                  child: Text(
                    _debugInfo.isEmpty ? 'اضغط على أحد الأزرار لبدء الاختبار' : _debugInfo,
                    style: const TextStyle(
                      color: Colors.white,
                      fontFamily: 'monospace',
                      fontSize: 12,
                    ),
                  ),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  void _addDebugInfo(String info) {
    setState(() {
      _debugInfo += '${DateTime.now().toString().substring(11, 19)}: $info\n';
    });
  }

  void _clearDebugInfo() {
    setState(() {
      _debugInfo = '';
    });
  }

  Future<void> _testDatabaseConnection() async {
    setState(() {
      _isLoading = true;
    });

    try {
      _addDebugInfo('🔄 اختبار الاتصال بقاعدة البيانات...');
      
      final ref = _database.ref('test');
      await ref.set('connection_test');
      _addDebugInfo('✅ تم الاتصال بنجاح');
      
      await ref.remove();
      _addDebugInfo('✅ تم حذف البيانات التجريبية');
      
    } catch (e) {
      _addDebugInfo('❌ فشل الاتصال: $e');
    }

    setState(() {
      _isLoading = false;
    });
  }

  Future<void> _testWritePermission() async {
    setState(() {
      _isLoading = true;
    });

    try {
      _addDebugInfo('🔄 اختبار صلاحيات الكتابة...');
      
      final user = FirebaseAuth.instance.currentUser;
      if (user == null) {
        _addDebugInfo('❌ لا يوجد مستخدم مسجل دخول');
        setState(() {
          _isLoading = false;
        });
        return;
      }

      final ref = _database.ref('users/${user.uid}/test');
      await ref.set({
        'timestamp': DateTime.now().millisecondsSinceEpoch,
        'test': true,
      });
      _addDebugInfo('✅ تم الكتابة بنجاح');
      
      await ref.remove();
      _addDebugInfo('✅ تم حذف البيانات التجريبية');
      
    } catch (e) {
      _addDebugInfo('❌ فشل في الكتابة: $e');
    }

    setState(() {
      _isLoading = false;
    });
  }

  Future<void> _testReadPermission() async {
    setState(() {
      _isLoading = true;
    });

    try {
      _addDebugInfo('🔄 اختبار صلاحيات القراءة...');
      
      final user = FirebaseAuth.instance.currentUser;
      if (user == null) {
        _addDebugInfo('❌ لا يوجد مستخدم مسجل دخول');
        setState(() {
          _isLoading = false;
        });
        return;
      }

      final ref = _database.ref('users/${user.uid}');
      final snapshot = await ref.get();
      
      if (snapshot.exists) {
        _addDebugInfo('✅ تم قراءة البيانات بنجاح');
        _addDebugInfo('📄 البيانات: ${snapshot.value}');
      } else {
        _addDebugInfo('⚠️ لا توجد بيانات للمستخدم');
      }
      
    } catch (e) {
      _addDebugInfo('❌ فشل في القراءة: $e');
    }

    setState(() {
      _isLoading = false;
    });
  }

  Future<void> _testUserCreation() async {
    setState(() {
      _isLoading = true;
    });

    try {
      _addDebugInfo('🔄 اختبار إنشاء بيانات مستخدم...');
      
      final user = FirebaseAuth.instance.currentUser;
      if (user == null) {
        _addDebugInfo('❌ لا يوجد مستخدم مسجل دخول');
        setState(() {
          _isLoading = false;
        });
        return;
      }

      final userModel = UserModel(
        uid: user.uid,
        email: user.email ?? '<EMAIL>',
        displayName: user.displayName ?? 'Test User',
        createdAt: DateTime.now(),
        lastSeen: DateTime.now(),
        isOnline: true,
        phoneNumber: '+1234567890',
        age: 25,
        bio: 'Test bio',
        location: 'Test location',
        status: 'متاح',
      );

      await _realtimeDB.saveUserData(userModel);
      _addDebugInfo('✅ تم إنشاء بيانات المستخدم بنجاح');
      
      // Test reading the data back
      final savedUser = await _realtimeDB.getUserData(user.uid);
      if (savedUser != null) {
        _addDebugInfo('✅ تم قراءة البيانات المحفوظة بنجاح');
        _addDebugInfo('📄 الاسم: ${savedUser.displayName}');
        _addDebugInfo('📄 البريد: ${savedUser.email}');
        _addDebugInfo('📄 الهاتف: ${savedUser.phoneNumber}');
        _addDebugInfo('📄 العمر: ${savedUser.age}');
      } else {
        _addDebugInfo('❌ فشل في قراءة البيانات المحفوظة');
      }
      
    } catch (e) {
      _addDebugInfo('❌ فشل في إنشاء بيانات المستخدم: $e');
    }

    setState(() {
      _isLoading = false;
    });
  }

  Future<void> _checkCurrentUser() async {
    setState(() {
      _isLoading = true;
    });

    try {
      _addDebugInfo('🔄 فحص المستخدم الحالي...');
      
      final user = FirebaseAuth.instance.currentUser;
      if (user == null) {
        _addDebugInfo('❌ لا يوجد مستخدم مسجل دخول');
      } else {
        _addDebugInfo('✅ يوجد مستخدم مسجل دخول');
        _addDebugInfo('📄 UID: ${user.uid}');
        _addDebugInfo('📄 البريد: ${user.email}');
        _addDebugInfo('📄 الاسم: ${user.displayName}');
        _addDebugInfo('📄 تم التحقق: ${user.emailVerified}');
        
        // Check if user data exists in Realtime Database
        final userData = await _realtimeDB.getUserData(user.uid);
        if (userData != null) {
          _addDebugInfo('✅ توجد بيانات في Realtime Database');
          _addDebugInfo('📄 الاسم المحفوظ: ${userData.displayName}');
          _addDebugInfo('📄 الهاتف: ${userData.phoneNumber ?? 'غير محدد'}');
          _addDebugInfo('📄 العمر: ${userData.age ?? 'غير محدد'}');
        } else {
          _addDebugInfo('❌ لا توجد بيانات في Realtime Database');
        }
      }
      
    } catch (e) {
      _addDebugInfo('❌ خطأ في فحص المستخدم: $e');
    }

    setState(() {
      _isLoading = false;
    });
  }
}
